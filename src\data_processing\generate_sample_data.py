"""
生成示例建筑能耗数据
用于演示和测试模型
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os


def generate_sample_building_energy_data(
    start_date: str = "2022-01-01",
    end_date: str = "2024-12-31",
    freq: str = "H",
    save_path: str = "data/sample/building_energy_sample.csv"
) -> pd.DataFrame:
    """
    生成示例建筑能耗数据
    
    Args:
        start_date: 开始日期
        end_date: 结束日期  
        freq: 数据频率 (H=小时, D=天)
        save_path: 保存路径
    
    Returns:
        DataFrame: 生成的数据
    """
    
    print("开始生成示例建筑能耗数据...")
    
    # 创建时间索引
    date_range = pd.date_range(start=start_date, end=end_date, freq=freq)
    n_samples = len(date_range)
    
    # 设置随机种子以确保可重现性
    np.random.seed(42)
    
    # 基础参数
    base_consumption = 100  # 基础能耗 (kWh)
    
    # 1. 生成温度数据 (摄氏度)
    # 年度周期 + 日周期 + 随机噪声
    day_of_year = date_range.dayofyear
    hour_of_day = date_range.hour
    
    # 年度温度变化 (正弦波)
    annual_temp = 15 + 10 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
    
    # 日温度变化
    daily_temp_variation = 5 * np.sin(2 * np.pi * (hour_of_day - 6) / 24)
    
    # 随机噪声
    temp_noise = np.random.normal(0, 2, n_samples)
    
    temperature = annual_temp + daily_temp_variation + temp_noise
    
    # 2. 生成湿度数据 (%)
    # 与温度负相关 + 随机变化
    base_humidity = 60
    humidity_temp_effect = -0.5 * (temperature - 20)
    humidity_noise = np.random.normal(0, 5, n_samples)
    humidity = np.clip(base_humidity + humidity_temp_effect + humidity_noise, 20, 90)
    
    # 3. 生成时间特征
    hour_of_day = date_range.hour
    day_of_week = date_range.dayofweek
    month = date_range.month
    is_weekend = (day_of_week >= 5).astype(int)
    
    # 简单的节假日标识 (假设每月1号和15号为节假日)
    is_holiday = ((date_range.day == 1) | (date_range.day == 15)).astype(int)
    
    # 4. 生成能耗数据 (复杂的多因素模型)
    
    # 基础负荷模式 (工作日vs周末)
    workday_pattern = np.where(
        is_weekend == 0,
        # 工作日模式: 早8点和晚6点高峰
        1.0 + 0.3 * np.sin(2 * np.pi * (hour_of_day - 8) / 12) + 
        0.2 * np.sin(2 * np.pi * (hour_of_day - 18) / 12),
        # 周末模式: 相对平缓
        0.8 + 0.2 * np.sin(2 * np.pi * (hour_of_day - 12) / 24)
    )
    
    # 季节性影响 (夏季制冷，冬季供暖)
    seasonal_effect = 1.0 + 0.4 * np.abs(np.sin(2 * np.pi * (day_of_year - 172) / 365))
    
    # 温度影响 (制冷和供暖需求)
    temp_effect = np.where(
        temperature > 25,  # 制冷需求
        1.0 + 0.02 * (temperature - 25) ** 1.5,
        np.where(
            temperature < 15,  # 供暖需求  
            1.0 + 0.03 * (15 - temperature) ** 1.2,
            1.0  # 舒适温度区间
        )
    )
    
    # 湿度影响 (除湿需求)
    humidity_effect = 1.0 + 0.005 * np.maximum(0, humidity - 70)
    
    # 节假日影响
    holiday_effect = np.where(is_holiday == 1, 0.7, 1.0)
    
    # 随机波动
    random_variation = np.random.normal(1.0, 0.1, n_samples)
    
    # 综合能耗计算
    energy_consumption = (
        base_consumption * 
        workday_pattern * 
        seasonal_effect * 
        temp_effect * 
        humidity_effect * 
        holiday_effect * 
        random_variation
    )
    
    # 添加一些异常值 (设备故障等)
    anomaly_indices = np.random.choice(n_samples, size=int(0.001 * n_samples), replace=False)
    energy_consumption[anomaly_indices] *= np.random.uniform(1.5, 3.0, len(anomaly_indices))
    
    # 确保能耗为正值
    energy_consumption = np.maximum(energy_consumption, 10)
    
    # 5. 创建DataFrame
    df = pd.DataFrame({
        'timestamp': date_range,
        'energy_consumption': energy_consumption,
        'temperature': temperature,
        'humidity': humidity,
        'hour_of_day': hour_of_day,
        'day_of_week': day_of_week,
        'month': month,
        'is_weekend': is_weekend,
        'is_holiday': is_holiday
    })
    
    # 6. 添加一些衍生特征作为示例
    df['temperature_squared'] = df['temperature'] ** 2
    df['temp_humidity_interaction'] = df['temperature'] * df['humidity'] / 100
    
    # 7. 保存数据
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    df.to_csv(save_path, index=False)
    
    print(f"示例数据生成完成!")
    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
    print(f"保存路径: {save_path}")
    print("\n数据统计:")
    print(df.describe())
    
    return df


def generate_multiple_building_data(
    n_buildings: int = 3,
    start_date: str = "2022-01-01",
    end_date: str = "2024-12-31"
):
    """生成多个建筑的能耗数据"""
    
    print(f"生成 {n_buildings} 个建筑的能耗数据...")
    
    for building_id in range(1, n_buildings + 1):
        # 为每个建筑设置不同的随机种子
        np.random.seed(42 + building_id)
        
        # 调整基础参数以模拟不同类型的建筑
        building_types = ['office', 'residential', 'commercial']
        building_type = building_types[(building_id - 1) % len(building_types)]
        
        save_path = f"data/sample/building_{building_id}_{building_type}_energy.csv"
        
        # 生成数据
        df = generate_sample_building_energy_data(
            start_date=start_date,
            end_date=end_date,
            save_path=save_path
        )
        
        print(f"建筑 {building_id} ({building_type}) 数据生成完成")


if __name__ == "__main__":
    # 生成单个建筑的示例数据
    sample_df = generate_sample_building_energy_data()
    
    # 生成多个建筑的数据
    generate_multiple_building_data(n_buildings=3)
    
    print("\n所有示例数据生成完成！")
