"""
模型评估脚本
包含完整的模型性能评估和可视化
"""

import os
import sys
import yaml
import numpy as np
import pandas as pd
import tensorflow as tf
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.data_processing.data_preprocessor import DataPreprocessor
from src.data_processing.generate_sample_data import generate_sample_building_energy_data


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.model = None
        self.data_processor = DataPreprocessor(config_path)
        self.predictions = {}
        self.metrics = {}
        
    def load_model(self, model_path: str = None):
        """加载训练好的模型"""
        if model_path is None:
            model_path = os.path.join(
                self.config['output']['model_save_path'],
                'best_model.h5'
            )
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        print(f"加载模型: {model_path}")
        self.model = tf.keras.models.load_model(model_path, compile=False)
        print("模型加载成功！")
        
        return self.model
    
    def load_data(self, data_file: str = None):
        """加载和处理数据"""
        print("=" * 50)
        print("加载评估数据...")
        
        # 如果没有提供数据文件，生成示例数据
        if data_file is None:
            data_file = "data/sample/building_energy_sample.csv"
            if not os.path.exists(data_file):
                print("生成示例数据...")
                generate_sample_building_energy_data(save_path=data_file)
        
        # 处理数据
        self.data = self.data_processor.process_data(data_file)
        
        if self.data is None:
            raise ValueError("数据处理失败")
        
        print("数据加载完成！")
        return self.data
    
    def make_predictions(self):
        """进行预测"""
        print("=" * 50)
        print("开始模型预测...")
        
        # 对各个数据集进行预测
        datasets = ['train', 'val', 'test']
        
        for dataset in datasets:
            X_key = f'X_{dataset}'
            y_key = f'y_{dataset}'
            
            if X_key in self.data and y_key in self.data:
                print(f"预测 {dataset} 数据集...")
                
                # 标准化数据预测
                y_pred_scaled = self.model.predict(self.data[X_key], verbose=0)
                
                # 反标准化预测结果
                y_pred = self.data_processor.inverse_transform_predictions(y_pred_scaled)
                
                # 获取真实值（原始数据）
                y_true_key = f'y_{dataset}_original'
                y_true = self.data[y_true_key]
                
                # 存储预测结果
                self.predictions[dataset] = {
                    'y_true': y_true,
                    'y_pred': y_pred.flatten(),
                    'y_pred_scaled': y_pred_scaled.flatten(),
                    'y_true_scaled': self.data[y_key]
                }
        
        print("预测完成！")
        return self.predictions
    
    def calculate_metrics(self):
        """计算评估指标"""
        print("=" * 50)
        print("计算评估指标...")
        
        for dataset in self.predictions.keys():
            y_true = self.predictions[dataset]['y_true']
            y_pred = self.predictions[dataset]['y_pred']
            
            # 计算各种指标
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            rmse = np.sqrt(mse)
            mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
            r2 = r2_score(y_true, y_pred)
            
            # 计算额外指标
            mean_true = np.mean(y_true)
            std_true = np.std(y_true)
            cv_rmse = (rmse / mean_true) * 100  # 变异系数
            
            self.metrics[dataset] = {
                'MAE': mae,
                'MSE': mse,
                'RMSE': rmse,
                'MAPE': mape,
                'R²': r2,
                'CV(RMSE)': cv_rmse,
                'Mean_True': mean_true,
                'Std_True': std_true
            }
            
            print(f"\n{dataset.upper()} 数据集指标:")
            print(f"  MAE:      {mae:.4f}")
            print(f"  RMSE:     {rmse:.4f}")
            print(f"  MAPE:     {mape:.2f}%")
            print(f"  R²:       {r2:.4f}")
            print(f"  CV(RMSE): {cv_rmse:.2f}%")
        
        return self.metrics
    
    def plot_predictions(self):
        """绘制预测结果"""
        print("=" * 50)
        print("绘制预测结果...")
        
        plt.style.use('seaborn-v0_8')
        
        # 创建子图
        n_datasets = len(self.predictions)
        fig, axes = plt.subplots(2, n_datasets, figsize=(6*n_datasets, 12))
        
        if n_datasets == 1:
            axes = axes.reshape(2, 1)
        
        fig.suptitle('模型预测结果分析', fontsize=16, fontweight='bold')
        
        for i, (dataset, pred_data) in enumerate(self.predictions.items()):
            y_true = pred_data['y_true']
            y_pred = pred_data['y_pred']
            
            # 第一行：时间序列对比图
            sample_size = min(500, len(y_true))  # 限制显示点数
            indices = np.linspace(0, len(y_true)-1, sample_size, dtype=int)
            
            axes[0, i].plot(indices, y_true[indices], label='真实值', alpha=0.7, linewidth=1)
            axes[0, i].plot(indices, y_pred[indices], label='预测值', alpha=0.7, linewidth=1)
            axes[0, i].set_title(f'{dataset.upper()} - 时间序列对比')
            axes[0, i].set_xlabel('时间点')
            axes[0, i].set_ylabel('能耗 (kWh)')
            axes[0, i].legend()
            axes[0, i].grid(True, alpha=0.3)
            
            # 第二行：散点图
            axes[1, i].scatter(y_true, y_pred, alpha=0.5, s=10)
            
            # 添加完美预测线
            min_val = min(y_true.min(), y_pred.min())
            max_val = max(y_true.max(), y_pred.max())
            axes[1, i].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='完美预测')
            
            # 添加指标文本
            r2 = self.metrics[dataset]['R²']
            rmse = self.metrics[dataset]['RMSE']
            axes[1, i].text(0.05, 0.95, f'R² = {r2:.3f}\nRMSE = {rmse:.2f}', 
                           transform=axes[1, i].transAxes, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            axes[1, i].set_title(f'{dataset.upper()} - 预测 vs 真实')
            axes[1, i].set_xlabel('真实值 (kWh)')
            axes[1, i].set_ylabel('预测值 (kWh)')
            axes[1, i].legend()
            axes[1, i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        plot_path = os.path.join(
            self.config['output']['results_save_path'],
            'prediction_results.png'
        )
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"预测结果图已保存到: {plot_path}")
    
    def plot_residuals(self):
        """绘制残差分析图"""
        print("绘制残差分析...")
        
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('残差分析', fontsize=16, fontweight='bold')
        
        # 使用测试集数据进行残差分析
        if 'test' in self.predictions:
            y_true = self.predictions['test']['y_true']
            y_pred = self.predictions['test']['y_pred']
            residuals = y_true - y_pred
            
            # 残差 vs 预测值
            axes[0, 0].scatter(y_pred, residuals, alpha=0.5)
            axes[0, 0].axhline(y=0, color='r', linestyle='--')
            axes[0, 0].set_title('残差 vs 预测值')
            axes[0, 0].set_xlabel('预测值')
            axes[0, 0].set_ylabel('残差')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 残差直方图
            axes[0, 1].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
            axes[0, 1].set_title('残差分布')
            axes[0, 1].set_xlabel('残差')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].grid(True, alpha=0.3)
            
            # Q-Q图
            from scipy import stats
            stats.probplot(residuals, dist="norm", plot=axes[1, 0])
            axes[1, 0].set_title('Q-Q图 (正态性检验)')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 残差时间序列
            sample_size = min(500, len(residuals))
            indices = np.linspace(0, len(residuals)-1, sample_size, dtype=int)
            axes[1, 1].plot(indices, residuals[indices], alpha=0.7)
            axes[1, 1].axhline(y=0, color='r', linestyle='--')
            axes[1, 1].set_title('残差时间序列')
            axes[1, 1].set_xlabel('时间点')
            axes[1, 1].set_ylabel('残差')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        plot_path = os.path.join(
            self.config['output']['results_save_path'],
            'residual_analysis.png'
        )
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"残差分析图已保存到: {plot_path}")
    
    def save_results(self):
        """保存评估结果"""
        print("=" * 50)
        print("保存评估结果...")
        
        # 保存指标
        metrics_path = os.path.join(
            self.config['output']['results_save_path'],
            'evaluation_metrics.json'
        )
        
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False)
        
        # 保存预测结果
        for dataset, pred_data in self.predictions.items():
            pred_df = pd.DataFrame({
                'y_true': pred_data['y_true'],
                'y_pred': pred_data['y_pred'],
                'residual': pred_data['y_true'] - pred_data['y_pred']
            })
            
            pred_path = os.path.join(
                self.config['output']['results_save_path'],
                f'predictions_{dataset}.csv'
            )
            pred_df.to_csv(pred_path, index=False)
        
        print(f"评估结果已保存到: {self.config['output']['results_save_path']}")
    
    def run_complete_evaluation(self, model_path: str = None, data_file: str = None):
        """运行完整的评估流程"""
        print("🔍 开始完整的模型评估流程...")
        print("=" * 60)
        
        try:
            # 1. 加载模型
            self.load_model(model_path)
            
            # 2. 加载数据
            self.load_data(data_file)
            
            # 3. 进行预测
            self.make_predictions()
            
            # 4. 计算指标
            self.calculate_metrics()
            
            # 5. 绘制结果
            self.plot_predictions()
            self.plot_residuals()
            
            # 6. 保存结果
            self.save_results()
            
            print("=" * 60)
            print("✅ 模型评估流程完成！")
            print(f"📁 结果保存在: {self.config['output']['results_save_path']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 评估过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("📊 建筑能耗预测模型评估")
    print("=" * 60)
    
    # 创建评估器
    evaluator = ModelEvaluator()
    
    # 运行完整评估流程
    success = evaluator.run_complete_evaluation()
    
    if success:
        print("\n🎉 评估成功完成！")
        print("📈 查看results目录中的详细结果")
    else:
        print("\n💥 评估失败，请检查错误信息")


if __name__ == "__main__":
    main()
