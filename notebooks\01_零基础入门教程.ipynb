{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏢 建筑能耗预测项目 - 零基础入门教程\n", "\n", "## 📚 教程概述\n", "\n", "欢迎来到建筑能耗预测项目！这个教程专门为零基础小白设计，将带你一步步了解：\n", "\n", "1. **什么是建筑能耗预测？**\n", "2. **什么是LSTM和CNN？**\n", "3. **如何运行这个项目？**\n", "4. **如何理解结果？**\n", "\n", "## 🎯 学习目标\n", "\n", "完成本教程后，你将能够：\n", "- 理解建筑能耗预测的基本概念\n", "- 了解深度学习模型的工作原理\n", "- 独立运行完整的预测项目\n", "- 分析和解释预测结果"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📖 第一部分：基础概念解释\n", "\n", "### 🏠 什么是建筑能耗预测？\n", "\n", "建筑能耗预测就是**预测建筑物未来的用电量**。比如：\n", "- 明天这栋办公楼会用多少电？\n", "- 下周这个商场的空调耗电量是多少？\n", "- 这个月住宅区的总用电量会是多少？\n", "\n", "### 🧠 什么是LSTM和CNN？\n", "\n", "**LSTM（长短期记忆网络）**：\n", "- 就像人的记忆一样，能记住过去的信息\n", "- 擅长处理时间序列数据（比如每小时的用电量）\n", "- 能发现\"昨天用电多，今天可能也会用电多\"这样的规律\n", "\n", "**CNN（卷积神经网络）**：\n", "- 就像放大镜一样，能发现数据中的局部模式\n", "- 擅长提取特征（比如\"每天早上8点用电量都会增加\"）\n", "- 能识别数据中的重复模式\n", "\n", "**CNN+LSTM组合**：\n", "- CNN先找出数据中的模式\n", "- LSTM再利用这些模式进行时间序列预测\n", "- 就像先用放大镜观察，再用记忆来预测"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 第二部分：项目结构介绍\n", "\n", "我们的项目就像一个工厂，有不同的车间：\n", "\n", "```\n", "📁 LSTM与CNN/                 # 项目根目录（工厂大门）\n", "├── 📁 data/                   # 数据仓库\n", "│   ├── 📁 raw/               # 原始数据（原材料）\n", "│   ├── 📁 processed/         # 处理后数据（半成品）\n", "│   └── 📁 sample/            # 示例数据（样品）\n", "├── 📁 src/                   # 源代码（生产车间）\n", "│   ├── 📁 models/            # 模型定义（机器设计图）\n", "│   ├── 📁 data_processing/   # 数据处理（原料加工车间）\n", "│   ├── 📁 training/          # 训练脚本（机器训练车间）\n", "│   └── 📁 evaluation/        # 评估脚本（质量检测车间）\n", "├── 📁 notebooks/             # 教程笔记本（说明书）\n", "├── 📁 results/               # 结果输出（成品仓库）\n", "├── 📄 requirements.txt       # 依赖包（工具清单）\n", "├── 📄 config.yaml           # 配置文件（生产参数）\n", "└── 📄 README.md             # 项目说明（使用手册）\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ 第三部分：环境准备\n", "\n", "在开始之前，我们需要准备\"工具\"（安装依赖包）："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 第一步：检查Python版本\n", "import sys\n", "print(f\"Python版本: {sys.version}\")\n", "\n", "# 建议使用Python 3.8或更高版本"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 第二步：安装必要的包（如果还没安装的话）\n", "# 在命令行中运行：pip install -r requirements.txt\n", "\n", "# 检查主要包是否已安装\n", "try:\n", "    import tensorflow as tf\n", "    import numpy as np\n", "    import pandas as pd\n", "    import matplotlib.pyplot as plt\n", "    import yaml\n", "    print(\"✅ 所有必要的包都已安装！\")\n", "    print(f\"TensorFlow版本: {tf.__version__}\")\n", "except ImportError as e:\n", "    print(f\"❌ 缺少包: {e}\")\n", "    print(\"请运行: pip install -r requirements.txt\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 第四部分：数据探索\n", "\n", "让我们先看看数据长什么样："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成示例数据\n", "import os\n", "import sys\n", "\n", "# 添加项目路径\n", "sys.path.append('..')\n", "\n", "from src.data_processing.generate_sample_data import generate_sample_building_energy_data\n", "\n", "# 生成示例数据\n", "print(\"🔄 正在生成示例数据...\")\n", "df = generate_sample_building_energy_data()\n", "print(\"✅ 数据生成完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查看数据的基本信息\n", "print(\"📋 数据基本信息:\")\n", "print(f\"数据形状: {df.shape}\")\n", "print(f\"时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}\")\n", "print(\"\\n📊 数据列名:\")\n", "for i, col in enumerate(df.columns, 1):\n", "    print(f\"{i:2d}. {col}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查看前几行数据\n", "print(\"👀 数据前5行:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据统计信息\n", "print(\"📈 数据统计信息:\")\n", "df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 第五部分：数据可视化\n", "\n", "让我们用图表来\"看见\"数据："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 设置中文字体和图表样式\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.style.use('seaborn-v0_8')\n", "\n", "# 创建图表\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "fig.suptitle('🏢 建筑能耗数据探索', fontsize=16, fontweight='bold')\n", "\n", "# 1. 能耗时间序列（显示一周的数据）\n", "week_data = df.head(24*7)  # 一周的小时数据\n", "axes[0, 0].plot(week_data['timestamp'], week_data['energy_consumption'], linewidth=1.5)\n", "axes[0, 0].set_title('📊 一周能耗变化')\n", "axes[0, 0].set_xlabel('时间')\n", "axes[0, 0].set_ylabel('能耗 (kWh)')\n", "axes[0, 0].tick_params(axis='x', rotation=45)\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# 2. 温度 vs 能耗\n", "sample_data = df.sample(1000)  # 随机采样1000个点\n", "axes[0, 1].scatter(sample_data['temperature'], sample_data['energy_consumption'], alpha=0.6)\n", "axes[0, 1].set_title('🌡️ 温度 vs 能耗关系')\n", "axes[0, 1].set_xlabel('温度 (°C)')\n", "axes[0, 1].set_ylabel('能耗 (kWh)')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 3. 每小时平均能耗\n", "hourly_avg = df.groupby('hour_of_day')['energy_consumption'].mean()\n", "axes[1, 0].bar(hourly_avg.index, hourly_avg.values, alpha=0.7)\n", "axes[1, 0].set_title('⏰ 每小时平均能耗')\n", "axes[1, 0].set_xlabel('小时')\n", "axes[1, 0].set_ylabel('平均能耗 (kWh)')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# 4. 工作日 vs 周末能耗\n", "weekend_comparison = df.groupby('is_weekend')['energy_consumption'].mean()\n", "labels = ['工作日', '周末']\n", "axes[1, 1].bar(labels, weekend_comparison.values, alpha=0.7, color=['skyblue', 'lightcoral'])\n", "axes[1, 1].set_title('📅 工作日 vs 周末能耗')\n", "axes[1, 1].set_ylabel('平均能耗 (kWh)')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n💡 从图表中我们可以看到:\")\n", "print(\"1. 能耗有明显的时间规律（白天高，夜晚低）\")\n", "print(\"2. 温度对能耗有影响（太热或太冷都会增加能耗）\")\n", "print(\"3. 工作时间的能耗通常比休息时间高\")\n", "print(\"4. 工作日和周末的能耗模式不同\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}