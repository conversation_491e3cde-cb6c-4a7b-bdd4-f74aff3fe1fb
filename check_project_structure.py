#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构检查和完善脚本
检查项目是否完整，并补充缺失的文件
"""

import os
import sys
from pathlib import Path


def check_project_structure():
    """检查项目结构"""
    print("🔍 检查项目结构...")
    
    # 定义期望的项目结构
    expected_structure = {
        # 根目录文件
        'files': [
            'README.md',
            'config.yaml', 
            'requirements.txt',
            'run_complete_project.py',
            'interactive_main.py',
            '零基础使用指南.md',
            'check_project_structure.py'
        ],
        
        # 目录结构
        'directories': {
            'data': ['raw', 'processed', 'sample'],
            'src': ['models', 'data_processing', 'training', 'evaluation', 'visualization'],
            'notebooks': [],
            'results': ['models', 'plots', 'logs']
        }
    }
    
    # 检查根目录文件
    print("\n📄 检查根目录文件:")
    missing_files = []
    for file in expected_structure['files']:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (缺失)")
            missing_files.append(file)
    
    # 检查目录结构
    print("\n📁 检查目录结构:")
    missing_dirs = []
    for main_dir, sub_dirs in expected_structure['directories'].items():
        if os.path.exists(main_dir):
            print(f"   ✅ {main_dir}/")
            
            # 检查子目录
            for sub_dir in sub_dirs:
                sub_path = os.path.join(main_dir, sub_dir)
                if os.path.exists(sub_path):
                    print(f"      ✅ {sub_path}/")
                else:
                    print(f"      ❌ {sub_path}/ (缺失)")
                    missing_dirs.append(sub_path)
        else:
            print(f"   ❌ {main_dir}/ (缺失)")
            missing_dirs.append(main_dir)
            for sub_dir in sub_dirs:
                missing_dirs.append(os.path.join(main_dir, sub_dir))
    
    # 检查源代码文件
    print("\n🐍 检查源代码文件:")
    src_files = {
        'src/__init__.py': '源代码包初始化文件',
        'src/models/__init__.py': '模型模块初始化文件',
        'src/models/cnn_lstm_model.py': 'CNN-LSTM模型定义',
        'src/data_processing/__init__.py': '数据处理模块初始化文件',
        'src/data_processing/data_preprocessor.py': '数据预处理器',
        'src/data_processing/generate_sample_data.py': '示例数据生成器',
        'src/training/__init__.py': '训练模块初始化文件',
        'src/training/train_model.py': '模型训练脚本',
        'src/evaluation/__init__.py': '评估模块初始化文件',
        'src/evaluation/evaluate_model.py': '模型评估脚本',
        'src/visualization/__init__.py': '可视化模块初始化文件',
        'src/visualization/advanced_plots.py': '高级可视化图表'
    }
    
    missing_src_files = []
    for file_path, description in src_files.items():
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} - {description}")
        else:
            print(f"   ❌ {file_path} - {description} (缺失)")
            missing_src_files.append(file_path)
    
    return missing_files, missing_dirs, missing_src_files


def create_missing_directories(missing_dirs):
    """创建缺失的目录"""
    if not missing_dirs:
        return
        
    print("\n📁 创建缺失的目录...")
    for dir_path in missing_dirs:
        try:
            os.makedirs(dir_path, exist_ok=True)
            print(f"   ✅ 已创建: {dir_path}")
        except Exception as e:
            print(f"   ❌ 创建失败 {dir_path}: {e}")


def create_missing_init_files(missing_src_files):
    """创建缺失的__init__.py文件"""
    init_files = [f for f in missing_src_files if f.endswith('__init__.py')]
    
    if not init_files:
        return
        
    print("\n🐍 创建缺失的__init__.py文件...")
    
    init_contents = {
        'src/__init__.py': '''"""
建筑能耗预测项目源代码包
"""

__version__ = "1.0.0"
__author__ = "Building Energy Prediction Team"
__description__ = "CNN-LSTM混合模型用于建筑能耗预测"
''',
        'src/models/__init__.py': '''"""
模型定义模块
"""

from .cnn_lstm_model import CNNLSTMModel, create_model, AttentionLayer

__all__ = ['CNNLSTMModel', 'create_model', 'AttentionLayer']
''',
        'src/data_processing/__init__.py': '''"""
数据处理模块
"""

from .data_preprocessor import DataPreprocessor
from .generate_sample_data import generate_sample_building_energy_data

__all__ = ['DataPreprocessor', 'generate_sample_building_energy_data']
''',
        'src/training/__init__.py': '''"""
训练模块
"""

from .train_model import ModelTrainer

__all__ = ['ModelTrainer']
''',
        'src/evaluation/__init__.py': '''"""
评估模块
"""

from .evaluate_model import ModelEvaluator

__all__ = ['ModelEvaluator']
''',
        'src/visualization/__init__.py': '''"""
可视化模块
"""

from .advanced_plots import AdvancedBuildingEnergyVisualizer, create_advanced_visualizations

__all__ = ['AdvancedBuildingEnergyVisualizer', 'create_advanced_visualizations']
'''
    }
    
    for file_path in init_files:
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 写入内容
            content = init_contents.get(file_path, '# 模块初始化文件\n')
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"   ✅ 已创建: {file_path}")
            
        except Exception as e:
            print(f"   ❌ 创建失败 {file_path}: {e}")


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'tensorflow', 'numpy', 'pandas', 'matplotlib', 'seaborn',
        'scikit-learn', 'pyyaml', 'plotly', 'statsmodels', 'networkx'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
    else:
        print("\n✅ 所有依赖包都已安装")
    
    return missing_packages


def generate_project_summary():
    """生成项目摘要"""
    print("\n📋 生成项目摘要...")
    
    summary = """
# 🏢 建筑能耗预测项目摘要

## 📊 项目统计
"""
    
    # 统计文件数量
    total_files = 0
    total_dirs = 0
    code_files = 0
    
    for root, dirs, files in os.walk('.'):
        if '.git' in root or '__pycache__' in root:
            continue
            
        total_dirs += len(dirs)
        total_files += len(files)
        
        for file in files:
            if file.endswith(('.py', '.md', '.yaml', '.yml')):
                code_files += 1
    
    summary += f"""
- 📁 总目录数: {total_dirs}
- 📄 总文件数: {total_files}
- 🐍 代码文件数: {code_files}

## 🎯 主要功能模块

### 1. 数据处理模块 (`src/data_processing/`)
- 数据预处理和清洗
- 特征工程
- 示例数据生成

### 2. 模型模块 (`src/models/`)
- CNN-LSTM混合模型
- 注意力机制
- 模型构建和编译

### 3. 训练模块 (`src/training/`)
- 模型训练流程
- 回调函数设置
- 训练历史记录

### 4. 评估模块 (`src/evaluation/`)
- 模型性能评估
- 预测结果分析
- 评估指标计算

### 5. 可视化模块 (`src/visualization/`)
- 10+种高级图表
- SCI级别分析图
- 交互式可视化

## 🚀 使用方式

### 零基础用户
```bash
python run_complete_project.py
```

### 交互式界面
```bash
python interactive_main.py
```

### 分步执行
```bash
# 1. 训练模型
python src/training/train_model.py

# 2. 评估模型  
python src/evaluation/evaluate_model.py
```

## 📈 输出结果

- `results/models/` - 训练好的模型
- `results/plots/` - 可视化图表
- `results/logs/` - 训练日志
- `results/` - 评估报告和预测结果
"""
    
    # 保存摘要
    with open('PROJECT_SUMMARY.md', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print("✅ 项目摘要已保存到 PROJECT_SUMMARY.md")


def main():
    """主函数"""
    print("🔧 建筑能耗预测项目结构检查工具")
    print("=" * 60)
    
    # 检查项目结构
    missing_files, missing_dirs, missing_src_files = check_project_structure()
    
    # 创建缺失的目录
    create_missing_directories(missing_dirs)
    
    # 创建缺失的初始化文件
    create_missing_init_files(missing_src_files)
    
    # 检查依赖
    missing_packages = check_dependencies()
    
    # 生成项目摘要
    generate_project_summary()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 检查结果总结:")
    
    if not missing_files and not missing_dirs and not missing_src_files:
        print("✅ 项目结构完整！")
    else:
        print("⚠️  发现缺失项目:")
        if missing_files:
            print(f"   - 缺失文件: {len(missing_files)} 个")
        if missing_dirs:
            print(f"   - 缺失目录: {len(missing_dirs)} 个")
        if missing_src_files:
            print(f"   - 缺失源码文件: {len(missing_src_files)} 个")
    
    if missing_packages:
        print(f"⚠️  缺失依赖包: {len(missing_packages)} 个")
        print("   请运行: pip install -r requirements.txt")
    else:
        print("✅ 所有依赖包都已安装")
    
    print("\n🎉 项目检查完成！")
    print("📖 查看 零基础使用指南.md 了解如何使用")
    print("🚀 运行 python interactive_main.py 启动交互式界面")


if __name__ == "__main__":
    main()
