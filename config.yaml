# 建筑能耗预测项目配置文件

# 数据配置
data:
  # 数据路径
  raw_data_path: "data/raw/"
  processed_data_path: "data/processed/"
  sample_data_path: "data/sample/"
  
  # 数据预处理参数
  sequence_length: 24  # 输入序列长度（小时）
  prediction_horizon: 1  # 预测步长
  train_ratio: 0.7  # 训练集比例
  val_ratio: 0.15   # 验证集比例
  test_ratio: 0.15  # 测试集比例
  
  # 特征列
  target_column: "energy_consumption"
  feature_columns:
    - "temperature"
    - "humidity" 
    - "hour_of_day"
    - "day_of_week"
    - "month"
    - "is_weekend"
    - "is_holiday"
  
  # 数据标准化
  normalization: "minmax"  # minmax, standard, robust

# 模型配置
model:
  # 模型类型
  model_type: "cnn_lstm_attention"  # cnn_lstm, cnn_lstm_attention, lstm_only
  
  # CNN参数
  cnn:
    filters: [64, 128, 64]  # 卷积核数量
    kernel_sizes: [3, 3, 3]  # 卷积核大小
    activation: "relu"
    dropout: 0.2
    
  # LSTM参数  
  lstm:
    units: [128, 64]  # LSTM单元数
    dropout: 0.2
    recurrent_dropout: 0.2
    return_sequences: true  # 除最后一层外都返回序列
    
  # 注意力机制参数
  attention:
    units: 64
    activation: "tanh"
    
  # 全连接层参数
  dense:
    units: [32, 16, 1]
    activation: "relu"
    dropout: 0.1

# 训练配置
training:
  # 基本参数
  batch_size: 32
  epochs: 100
  learning_rate: 0.001
  
  # 优化器
  optimizer: "adam"  # adam, rmsprop, sgd
  
  # 损失函数
  loss: "mse"  # mse, mae, huber
  
  # 评估指标
  metrics: ["mae", "mse"]
  
  # 早停参数
  early_stopping:
    monitor: "val_loss"
    patience: 15
    restore_best_weights: true
    
  # 学习率调度
  lr_scheduler:
    factor: 0.5
    patience: 10
    min_lr: 0.00001
    
  # 模型检查点
  checkpoint:
    monitor: "val_loss"
    save_best_only: true
    save_weights_only: false

# 评估配置
evaluation:
  # 评估指标
  metrics:
    - "mae"
    - "rmse" 
    - "mape"
    - "r2_score"
    
  # 可视化
  visualization:
    plot_predictions: true
    plot_residuals: true
    plot_feature_importance: true
    save_plots: true

# 输出配置
output:
  # 模型保存路径
  model_save_path: "results/models/"
  
  # 结果保存路径
  results_save_path: "results/"
  
  # 日志配置
  log_level: "INFO"
  log_file: "results/training.log"
  
  # 随机种子
  random_seed: 42
