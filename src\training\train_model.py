"""
模型训练脚本
包含完整的训练流程和回调函数
"""

import os
import sys
import yaml
import numpy as np
import tensorflow as tf
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.models.cnn_lstm_model import create_model
from src.data_processing.data_preprocessor import DataPreprocessor
from src.data_processing.generate_sample_data import generate_sample_building_energy_data


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.model = None
        self.history = None
        self.data_processor = DataPreprocessor(config_path)
        
        # 设置随机种子
        self._set_random_seeds()
        
        # 创建输出目录
        self._create_output_directories()
    
    def _set_random_seeds(self):
        """设置随机种子以确保可重现性"""
        seed = self.config['output']['random_seed']
        np.random.seed(seed)
        tf.random.set_seed(seed)
        os.environ['PYTHONHASHSEED'] = str(seed)
    
    def _create_output_directories(self):
        """创建输出目录"""
        directories = [
            self.config['output']['model_save_path'],
            self.config['output']['results_save_path'],
            os.path.dirname(self.config['output']['log_file'])
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def prepare_data(self, data_file: str = None):
        """准备训练数据"""
        print("=" * 50)
        print("开始数据准备...")
        
        # 如果没有提供数据文件，生成示例数据
        if data_file is None:
            print("未提供数据文件，生成示例数据...")
            data_file = "data/sample/building_energy_sample.csv"
            generate_sample_building_energy_data(save_path=data_file)
        
        # 处理数据
        self.data = self.data_processor.process_data(data_file)
        
        if self.data is None:
            raise ValueError("数据处理失败")
        
        print("数据准备完成！")
        print(f"训练集形状: {self.data['X_train'].shape}")
        print(f"验证集形状: {self.data['X_val'].shape}")
        print(f"测试集形状: {self.data['X_test'].shape}")
        
        return self.data
    
    def build_model(self):
        """构建模型"""
        print("=" * 50)
        print("开始构建模型...")
        
        input_shape = self.data['input_shape']
        self.model, self.model_builder = create_model(self.config, input_shape)
        
        print("模型构建完成！")
        print("\n模型架构:")
        self.model.summary()
        
        return self.model
    
    def setup_callbacks(self):
        """设置训练回调函数"""
        callbacks = []
        
        # 早停回调
        early_stopping_config = self.config['training']['early_stopping']
        early_stopping = EarlyStopping(
            monitor=early_stopping_config['monitor'],
            patience=early_stopping_config['patience'],
            restore_best_weights=early_stopping_config['restore_best_weights'],
            verbose=1
        )
        callbacks.append(early_stopping)
        
        # 模型检查点回调
        checkpoint_config = self.config['training']['checkpoint']
        model_save_path = os.path.join(
            self.config['output']['model_save_path'],
            'best_model.h5'
        )
        checkpoint = ModelCheckpoint(
            filepath=model_save_path,
            monitor=checkpoint_config['monitor'],
            save_best_only=checkpoint_config['save_best_only'],
            save_weights_only=checkpoint_config['save_weights_only'],
            verbose=1
        )
        callbacks.append(checkpoint)
        
        # 学习率调度回调
        lr_scheduler_config = self.config['training']['lr_scheduler']
        lr_scheduler = ReduceLROnPlateau(
            monitor='val_loss',
            factor=lr_scheduler_config['factor'],
            patience=lr_scheduler_config['patience'],
            min_lr=lr_scheduler_config['min_lr'],
            verbose=1
        )
        callbacks.append(lr_scheduler)
        
        return callbacks
    
    def train_model(self):
        """训练模型"""
        print("=" * 50)
        print("开始模型训练...")
        
        # 设置回调函数
        callbacks = self.setup_callbacks()
        
        # 训练参数
        training_config = self.config['training']
        
        # 开始训练
        start_time = datetime.now()
        
        self.history = self.model.fit(
            self.data['X_train'],
            self.data['y_train'],
            batch_size=training_config['batch_size'],
            epochs=training_config['epochs'],
            validation_data=(self.data['X_val'], self.data['y_val']),
            callbacks=callbacks,
            verbose=1
        )
        
        end_time = datetime.now()
        training_time = end_time - start_time
        
        print(f"模型训练完成！")
        print(f"训练时间: {training_time}")
        
        # 保存训练历史
        self._save_training_history()
        
        return self.history
    
    def _save_training_history(self):
        """保存训练历史"""
        history_path = os.path.join(
            self.config['output']['results_save_path'],
            'training_history.json'
        )
        
        # 转换numpy数组为列表以便JSON序列化
        history_dict = {}
        for key, values in self.history.history.items():
            history_dict[key] = [float(v) for v in values]
        
        with open(history_path, 'w') as f:
            json.dump(history_dict, f, indent=2)
        
        print(f"训练历史已保存到: {history_path}")
    
    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("没有训练历史可绘制")
            return
        
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('模型训练历史', fontsize=16, fontweight='bold')
        
        # 损失函数
        axes[0, 0].plot(self.history.history['loss'], label='训练损失', linewidth=2)
        axes[0, 0].plot(self.history.history['val_loss'], label='验证损失', linewidth=2)
        axes[0, 0].set_title('模型损失')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # MAE指标
        if 'mae' in self.history.history:
            axes[0, 1].plot(self.history.history['mae'], label='训练MAE', linewidth=2)
            axes[0, 1].plot(self.history.history['val_mae'], label='验证MAE', linewidth=2)
            axes[0, 1].set_title('平均绝对误差 (MAE)')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('MAE')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # 学习率
        if 'lr' in self.history.history:
            axes[1, 0].plot(self.history.history['lr'], linewidth=2, color='orange')
            axes[1, 0].set_title('学习率变化')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Learning Rate')
            axes[1, 0].set_yscale('log')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 训练进度总览
        epochs = range(1, len(self.history.history['loss']) + 1)
        axes[1, 1].plot(epochs, self.history.history['loss'], 'b-', label='训练损失')
        axes[1, 1].plot(epochs, self.history.history['val_loss'], 'r-', label='验证损失')
        
        # 标记最佳epoch
        best_epoch = np.argmin(self.history.history['val_loss']) + 1
        best_val_loss = min(self.history.history['val_loss'])
        axes[1, 1].axvline(x=best_epoch, color='green', linestyle='--', alpha=0.7)
        axes[1, 1].scatter([best_epoch], [best_val_loss], color='green', s=100, zorder=5)
        axes[1, 1].annotate(f'最佳: Epoch {best_epoch}', 
                           xy=(best_epoch, best_val_loss),
                           xytext=(best_epoch + 5, best_val_loss + 0.01),
                           arrowprops=dict(arrowstyle='->', color='green'))
        
        axes[1, 1].set_title('训练进度总览')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        plot_path = os.path.join(
            self.config['output']['results_save_path'],
            'training_history.png'
        )
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"训练历史图已保存到: {plot_path}")
    
    def save_model_info(self):
        """保存模型信息"""
        model_info = {
            'model_type': self.config['model']['model_type'],
            'input_shape': self.data['input_shape'],
            'total_parameters': self.model.count_params(),
            'trainable_parameters': sum([tf.keras.backend.count_params(w) for w in self.model.trainable_weights]),
            'config': self.config,
            'training_completed': datetime.now().isoformat()
        }
        
        info_path = os.path.join(
            self.config['output']['results_save_path'],
            'model_info.json'
        )
        
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        print(f"模型信息已保存到: {info_path}")
    
    def run_complete_training(self, data_file: str = None):
        """运行完整的训练流程"""
        print("🚀 开始完整的模型训练流程...")
        print("=" * 60)
        
        try:
            # 1. 准备数据
            self.prepare_data(data_file)
            
            # 2. 构建模型
            self.build_model()
            
            # 3. 训练模型
            self.train_model()
            
            # 4. 绘制训练历史
            self.plot_training_history()
            
            # 5. 保存模型信息
            self.save_model_info()
            
            print("=" * 60)
            print("✅ 模型训练流程完成！")
            print(f"📁 结果保存在: {self.config['output']['results_save_path']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🏢 建筑能耗预测模型训练")
    print("=" * 60)
    
    # 创建训练器
    trainer = ModelTrainer()
    
    # 运行完整训练流程
    success = trainer.run_complete_training()
    
    if success:
        print("\n🎉 训练成功完成！")
        print("📊 接下来可以运行评估脚本查看模型性能")
    else:
        print("\n💥 训练失败，请检查错误信息")


if __name__ == "__main__":
    main()
