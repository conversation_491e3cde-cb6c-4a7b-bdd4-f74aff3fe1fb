"""
高级可视化模块 - 建筑学SCI级别分析图表
包含10+种专业级别的数据分析图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

# 设置科学出版级别的图表样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedBuildingEnergyVisualizer:
    """高级建筑能耗可视化分析器"""
    
    def __init__(self, data, save_path="results/plots/"):
        self.data = data
        self.save_path = save_path
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72', 
            'accent': '#F18F01',
            'success': '#C73E1D',
            'info': '#7209B7',
            'warning': '#F4A261',
            'light': '#E9C46A',
            'dark': '#264653'
        }
        
    def plot_1_temporal_heatmap(self):
        """图1: 时间-能耗热力图 (SCI级别)"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Temporal Energy Consumption Patterns Analysis', 
                    fontsize=16, fontweight='bold', y=0.95)
        
        # 准备数据
        df = self.data.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.hour
        df['day'] = df['timestamp'].dt.day
        df['month'] = df['timestamp'].dt.month
        df['weekday'] = df['timestamp'].dt.dayofweek
        
        # 1. 小时-月份热力图
        hourly_monthly = df.groupby(['month', 'hour'])['energy_consumption'].mean().unstack()
        im1 = axes[0,0].imshow(hourly_monthly.values, cmap='YlOrRd', aspect='auto')
        axes[0,0].set_title('(a) Monthly-Hourly Energy Consumption Pattern', fontweight='bold')
        axes[0,0].set_xlabel('Hour of Day')
        axes[0,0].set_ylabel('Month')
        axes[0,0].set_xticks(range(0, 24, 4))
        axes[0,0].set_xticklabels(range(0, 24, 4))
        axes[0,0].set_yticks(range(12))
        axes[0,0].set_yticklabels(['Jan','Feb','Mar','Apr','May','Jun',
                                  'Jul','Aug','Sep','Oct','Nov','Dec'])
        plt.colorbar(im1, ax=axes[0,0], label='Energy (kWh)')
        
        # 2. 工作日-小时热力图
        weekday_hourly = df.groupby(['weekday', 'hour'])['energy_consumption'].mean().unstack()
        im2 = axes[0,1].imshow(weekday_hourly.values, cmap='viridis', aspect='auto')
        axes[0,1].set_title('(b) Weekday-Hourly Energy Consumption Pattern', fontweight='bold')
        axes[0,1].set_xlabel('Hour of Day')
        axes[0,1].set_ylabel('Day of Week')
        axes[0,1].set_xticks(range(0, 24, 4))
        axes[0,1].set_xticklabels(range(0, 24, 4))
        axes[0,1].set_yticks(range(7))
        axes[0,1].set_yticklabels(['Mon','Tue','Wed','Thu','Fri','Sat','Sun'])
        plt.colorbar(im2, ax=axes[0,1], label='Energy (kWh)')
        
        # 3. 温度-能耗关系热力图
        temp_bins = pd.cut(df['temperature'], bins=20)
        hour_bins = pd.cut(df['hour'], bins=12)
        temp_hour_energy = df.groupby([temp_bins, hour_bins])['energy_consumption'].mean().unstack()
        im3 = axes[1,0].imshow(temp_hour_energy.values, cmap='plasma', aspect='auto')
        axes[1,0].set_title('(c) Temperature-Time Energy Consumption Correlation', fontweight='bold')
        axes[1,0].set_xlabel('Time Period (2-hour bins)')
        axes[1,0].set_ylabel('Temperature Range (°C)')
        plt.colorbar(im3, ax=axes[1,0], label='Energy (kWh)')
        
        # 4. 能耗强度分布
        energy_intensity = df['energy_consumption'] / df['energy_consumption'].mean()
        axes[1,1].hist2d(df['hour'], energy_intensity, bins=24, cmap='coolwarm')
        axes[1,1].set_title('(d) Energy Intensity Distribution by Hour', fontweight='bold')
        axes[1,1].set_xlabel('Hour of Day')
        axes[1,1].set_ylabel('Energy Intensity (Normalized)')
        
        plt.tight_layout()
        plt.savefig(f'{self.save_path}01_temporal_heatmap_analysis.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
    def plot_2_energy_flow_sankey(self):
        """图2: 能耗流向桑基图"""
        df = self.data.copy()
        
        # 创建能耗分类
        df['energy_level'] = pd.cut(df['energy_consumption'], 
                                   bins=5, labels=['Very Low', 'Low', 'Medium', 'High', 'Very High'])
        df['temp_category'] = pd.cut(df['temperature'], 
                                    bins=3, labels=['Cold', 'Moderate', 'Hot'])
        df['time_period'] = pd.cut(df['hour_of_day'], 
                                  bins=4, labels=['Night', 'Morning', 'Afternoon', 'Evening'])
        
        # 使用plotly创建桑基图
        fig = go.Figure(data=[go.Sankey(
            node = dict(
                pad = 15,
                thickness = 20,
                line = dict(color = "black", width = 0.5),
                label = ["Cold", "Moderate", "Hot", "Night", "Morning", "Afternoon", "Evening",
                        "Very Low", "Low", "Medium", "High", "Very High"],
                color = ["lightblue", "lightgreen", "lightcoral", "navy", "orange", "gold", "purple",
                        "lightgray", "gray", "yellow", "orange", "red"]
            ),
            link = dict(
                source = [0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6],
                target = [7, 8, 8, 9, 9, 10, 7, 8, 9, 10, 10, 11, 10, 11],
                value = [20, 30, 40, 35, 25, 45, 15, 25, 35, 40, 30, 35, 25, 30]
            )
        )])
        
        fig.update_layout(
            title_text="Energy Flow Analysis: Temperature → Time → Consumption Level",
            font_size=12,
            width=1000,
            height=600
        )
        
        fig.write_html(f'{self.save_path}02_energy_flow_sankey.html')
        fig.show()
        
    def plot_3_3d_energy_landscape(self):
        """图3: 3D能耗地形图"""
        df = self.data.copy()
        
        # 创建3D数据
        temp_grid = np.linspace(df['temperature'].min(), df['temperature'].max(), 30)
        hour_grid = np.linspace(0, 23, 24)
        temp_mesh, hour_mesh = np.meshgrid(temp_grid, hour_grid)
        
        # 插值能耗数据
        from scipy.interpolate import griddata
        points = df[['temperature', 'hour_of_day']].values
        values = df['energy_consumption'].values
        energy_mesh = griddata(points, values, (temp_mesh, hour_mesh), method='cubic')
        
        fig = go.Figure(data=[go.Surface(
            x=temp_mesh,
            y=hour_mesh, 
            z=energy_mesh,
            colorscale='Viridis',
            colorbar=dict(title="Energy Consumption (kWh)")
        )])
        
        fig.update_layout(
            title='3D Energy Consumption Landscape',
            scene=dict(
                xaxis_title='Temperature (°C)',
                yaxis_title='Hour of Day',
                zaxis_title='Energy Consumption (kWh)',
                camera=dict(eye=dict(x=1.5, y=1.5, z=1.5))
            ),
            width=1000,
            height=700
        )
        
        fig.write_html(f'{self.save_path}03_3d_energy_landscape.html')
        fig.show()
        
    def plot_4_statistical_distribution_analysis(self):
        """图4: 统计分布分析"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Statistical Distribution Analysis of Building Energy Consumption', 
                    fontsize=16, fontweight='bold')
        
        df = self.data.copy()
        
        # 1. 能耗分布直方图 + 拟合曲线
        axes[0,0].hist(df['energy_consumption'], bins=50, density=True, alpha=0.7, 
                      color=self.colors['primary'], edgecolor='black')
        
        # 拟合正态分布
        mu, sigma = stats.norm.fit(df['energy_consumption'])
        x = np.linspace(df['energy_consumption'].min(), df['energy_consumption'].max(), 100)
        axes[0,0].plot(x, stats.norm.pdf(x, mu, sigma), 'r-', linewidth=2, 
                      label=f'Normal fit (μ={mu:.2f}, σ={sigma:.2f})')
        
        # 拟合对数正态分布
        s, loc, scale = stats.lognorm.fit(df['energy_consumption'])
        axes[0,0].plot(x, stats.lognorm.pdf(x, s, loc, scale), 'g--', linewidth=2,
                      label=f'Lognormal fit')
        
        axes[0,0].set_title('(a) Energy Consumption Distribution', fontweight='bold')
        axes[0,0].set_xlabel('Energy Consumption (kWh)')
        axes[0,0].set_ylabel('Probability Density')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. Q-Q图
        stats.probplot(df['energy_consumption'], dist="norm", plot=axes[0,1])
        axes[0,1].set_title('(b) Q-Q Plot (Normality Test)', fontweight='bold')
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. 箱线图 - 不同时间段
        df['time_category'] = pd.cut(df['hour_of_day'], 
                                    bins=[0, 6, 12, 18, 24], 
                                    labels=['Night', 'Morning', 'Afternoon', 'Evening'])
        
        box_data = [df[df['time_category'] == cat]['energy_consumption'].dropna() 
                   for cat in ['Night', 'Morning', 'Afternoon', 'Evening']]
        
        bp = axes[0,2].boxplot(box_data, labels=['Night', 'Morning', 'Afternoon', 'Evening'],
                              patch_artist=True, notch=True)
        
        colors = [self.colors['dark'], self.colors['warning'], self.colors['accent'], self.colors['secondary']]
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
            
        axes[0,2].set_title('(c) Energy Distribution by Time Period', fontweight='bold')
        axes[0,2].set_ylabel('Energy Consumption (kWh)')
        axes[0,2].grid(True, alpha=0.3)
        
        # 4. 小提琴图 - 温度分组
        df['temp_group'] = pd.cut(df['temperature'], bins=5, 
                                 labels=['Very Cold', 'Cold', 'Moderate', 'Warm', 'Hot'])
        
        violin_data = [df[df['temp_group'] == group]['energy_consumption'].dropna() 
                      for group in df['temp_group'].cat.categories]
        
        parts = axes[1,0].violinplot(violin_data, positions=range(1, 6), showmeans=True)
        for pc, color in zip(parts['bodies'], sns.color_palette("husl", 5)):
            pc.set_facecolor(color)
            pc.set_alpha(0.7)
            
        axes[1,0].set_xticks(range(1, 6))
        axes[1,0].set_xticklabels(['Very Cold', 'Cold', 'Moderate', 'Warm', 'Hot'], rotation=45)
        axes[1,0].set_title('(d) Energy Distribution by Temperature', fontweight='bold')
        axes[1,0].set_ylabel('Energy Consumption (kWh)')
        axes[1,0].grid(True, alpha=0.3)
        
        # 5. 累积分布函数
        sorted_energy = np.sort(df['energy_consumption'])
        y = np.arange(1, len(sorted_energy) + 1) / len(sorted_energy)
        axes[1,1].plot(sorted_energy, y, linewidth=2, color=self.colors['primary'])
        axes[1,1].set_title('(e) Cumulative Distribution Function', fontweight='bold')
        axes[1,1].set_xlabel('Energy Consumption (kWh)')
        axes[1,1].set_ylabel('Cumulative Probability')
        axes[1,1].grid(True, alpha=0.3)
        
        # 6. 核密度估计对比
        for season in [1, 4, 7, 10]:  # 代表四季
            season_data = df[df['month'] == season]['energy_consumption']
            if len(season_data) > 0:
                season_names = {1: 'Winter', 4: 'Spring', 7: 'Summer', 10: 'Autumn'}
                sns.kdeplot(data=season_data, ax=axes[1,2], label=season_names[season])
                
        axes[1,2].set_title('(f) Seasonal Energy Consumption Density', fontweight='bold')
        axes[1,2].set_xlabel('Energy Consumption (kWh)')
        axes[1,2].set_ylabel('Density')
        axes[1,2].legend()
        axes[1,2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.save_path}04_statistical_distribution_analysis.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
    def plot_5_correlation_network(self):
        """图5: 相关性网络图"""
        df = self.data.copy()
        
        # 选择数值特征
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        corr_matrix = df[numeric_cols].corr()
        
        # 创建网络图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # 1. 相关性热力图
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
                   square=True, ax=ax1, cbar_kws={"shrink": .8})
        ax1.set_title('(a) Feature Correlation Heatmap', fontweight='bold', fontsize=14)
        
        # 2. 网络图
        import networkx as nx
        
        # 创建网络
        G = nx.Graph()
        
        # 添加节点
        for col in numeric_cols:
            G.add_node(col)
        
        # 添加边（只保留强相关性）
        threshold = 0.3
        for i, col1 in enumerate(numeric_cols):
            for j, col2 in enumerate(numeric_cols):
                if i < j and abs(corr_matrix.loc[col1, col2]) > threshold:
                    G.add_edge(col1, col2, weight=abs(corr_matrix.loc[col1, col2]))
        
        # 绘制网络
        pos = nx.spring_layout(G, k=2, iterations=50)
        
        # 绘制节点
        node_colors = [self.colors['primary'] if 'energy' in node.lower() 
                      else self.colors['secondary'] for node in G.nodes()]
        nx.draw_networkx_nodes(G, pos, node_color=node_colors, 
                              node_size=1000, alpha=0.8, ax=ax2)
        
        # 绘制边
        edges = G.edges()
        weights = [G[u][v]['weight'] for u, v in edges]
        nx.draw_networkx_edges(G, pos, width=[w*3 for w in weights], 
                              alpha=0.6, edge_color='gray', ax=ax2)
        
        # 添加标签
        nx.draw_networkx_labels(G, pos, font_size=8, font_weight='bold', ax=ax2)
        
        ax2.set_title('(b) Feature Correlation Network', fontweight='bold', fontsize=14)
        ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(f'{self.save_path}05_correlation_network.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
    def plot_6_energy_efficiency_radar(self):
        """图6: 能效雷达图"""
        df = self.data.copy()

        # 计算不同维度的能效指标
        metrics = {}

        # 按时间段计算平均能耗
        time_periods = ['Night', 'Morning', 'Afternoon', 'Evening']
        df['time_period'] = pd.cut(df['hour_of_day'],
                                  bins=[0, 6, 12, 18, 24],
                                  labels=time_periods)

        for period in time_periods:
            period_data = df[df['time_period'] == period]['energy_consumption']
            metrics[f'{period}_Efficiency'] = 100 - (period_data.mean() / df['energy_consumption'].max() * 100)

        # 按温度范围计算能效
        temp_ranges = ['Cold', 'Moderate', 'Hot']
        df['temp_range'] = pd.cut(df['temperature'], bins=3, labels=temp_ranges)

        for temp in temp_ranges:
            temp_data = df[df['temp_range'] == temp]['energy_consumption']
            metrics[f'{temp}_Efficiency'] = 100 - (temp_data.mean() / df['energy_consumption'].max() * 100)

        # 创建雷达图
        categories = list(metrics.keys())
        values = list(metrics.values())

        fig = go.Figure()

        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name='Energy Efficiency',
            line_color='rgb(46, 134, 171)',
            fillcolor='rgba(46, 134, 171, 0.3)'
        ))

        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title="Building Energy Efficiency Radar Chart",
            width=800,
            height=600
        )

        fig.write_html(f'{self.save_path}06_energy_efficiency_radar.html')
        fig.show()

    def plot_7_seasonal_decomposition(self):
        """图7: 季节性分解图"""
        from statsmodels.tsa.seasonal import seasonal_decompose

        df = self.data.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp').sort_index()

        # 重采样为日数据
        daily_energy = df['energy_consumption'].resample('D').mean()

        # 季节性分解
        decomposition = seasonal_decompose(daily_energy, model='additive', period=30)

        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        fig.suptitle('Seasonal Decomposition of Energy Consumption', fontsize=16, fontweight='bold')

        # 原始数据
        decomposition.observed.plot(ax=axes[0], color=self.colors['primary'], linewidth=1.5)
        axes[0].set_title('(a) Original Time Series', fontweight='bold')
        axes[0].set_ylabel('Energy (kWh)')
        axes[0].grid(True, alpha=0.3)

        # 趋势
        decomposition.trend.plot(ax=axes[1], color=self.colors['secondary'], linewidth=2)
        axes[1].set_title('(b) Trend Component', fontweight='bold')
        axes[1].set_ylabel('Trend')
        axes[1].grid(True, alpha=0.3)

        # 季节性
        decomposition.seasonal.plot(ax=axes[2], color=self.colors['accent'], linewidth=1.5)
        axes[2].set_title('(c) Seasonal Component', fontweight='bold')
        axes[2].set_ylabel('Seasonal')
        axes[2].grid(True, alpha=0.3)

        # 残差
        decomposition.resid.plot(ax=axes[3], color=self.colors['success'], linewidth=1)
        axes[3].set_title('(d) Residual Component', fontweight='bold')
        axes[3].set_ylabel('Residual')
        axes[3].set_xlabel('Date')
        axes[3].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{self.save_path}07_seasonal_decomposition.png',
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()

    def plot_8_energy_clustering_analysis(self):
        """图8: 能耗聚类分析"""
        df = self.data.copy()

        # 准备聚类特征
        features = ['energy_consumption', 'temperature', 'humidity', 'hour_of_day']
        X = df[features].dropna()

        # 标准化
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # K-means聚类
        kmeans = KMeans(n_clusters=4, random_state=42)
        clusters = kmeans.fit_predict(X_scaled)

        # PCA降维可视化
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_scaled)

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Energy Consumption Clustering Analysis', fontsize=16, fontweight='bold')

        # 1. PCA聚类结果
        scatter = axes[0,0].scatter(X_pca[:, 0], X_pca[:, 1], c=clusters,
                                   cmap='viridis', alpha=0.6, s=30)
        axes[0,0].set_title('(a) PCA Clustering Results', fontweight='bold')
        axes[0,0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
        axes[0,0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.colorbar(scatter, ax=axes[0,0], label='Cluster')
        axes[0,0].grid(True, alpha=0.3)

        # 2. 温度-能耗聚类
        axes[0,1].scatter(X['temperature'], X['energy_consumption'], c=clusters,
                         cmap='viridis', alpha=0.6, s=30)
        axes[0,1].set_title('(b) Temperature vs Energy Clustering', fontweight='bold')
        axes[0,1].set_xlabel('Temperature (°C)')
        axes[0,1].set_ylabel('Energy Consumption (kWh)')
        axes[0,1].grid(True, alpha=0.3)

        # 3. 聚类中心特征
        cluster_centers = scaler.inverse_transform(kmeans.cluster_centers_)
        cluster_df = pd.DataFrame(cluster_centers, columns=features)

        x_pos = np.arange(len(features))
        width = 0.2

        for i in range(4):
            axes[1,0].bar(x_pos + i*width, cluster_df.iloc[i], width,
                         label=f'Cluster {i}', alpha=0.8)

        axes[1,0].set_title('(c) Cluster Centers Characteristics', fontweight='bold')
        axes[1,0].set_xlabel('Features')
        axes[1,0].set_ylabel('Normalized Values')
        axes[1,0].set_xticks(x_pos + width * 1.5)
        axes[1,0].set_xticklabels(features, rotation=45)
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)

        # 4. 聚类大小分布
        cluster_counts = pd.Series(clusters).value_counts().sort_index()
        colors = plt.cm.viridis(np.linspace(0, 1, 4))

        wedges, texts, autotexts = axes[1,1].pie(cluster_counts.values,
                                                labels=[f'Cluster {i}' for i in cluster_counts.index],
                                                autopct='%1.1f%%', colors=colors)
        axes[1,1].set_title('(d) Cluster Size Distribution', fontweight='bold')

        plt.tight_layout()
        plt.savefig(f'{self.save_path}08_energy_clustering_analysis.png',
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()

    def plot_9_energy_anomaly_detection(self):
        """图9: 能耗异常检测"""
        df = self.data.copy()

        # 使用Isolation Forest检测异常
        from sklearn.ensemble import IsolationForest

        features = ['energy_consumption', 'temperature', 'humidity']
        X = df[features].dropna()

        # 异常检测
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomalies = iso_forest.fit_predict(X)

        # 统计方法检测异常
        Q1 = df['energy_consumption'].quantile(0.25)
        Q3 = df['energy_consumption'].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        statistical_anomalies = (df['energy_consumption'] < lower_bound) | (df['energy_consumption'] > upper_bound)

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Energy Consumption Anomaly Detection Analysis', fontsize=16, fontweight='bold')

        # 1. 时间序列异常点
        normal_mask = anomalies == 1
        anomaly_mask = anomalies == -1

        axes[0,0].scatter(range(len(df)), df['energy_consumption'],
                         c='blue', alpha=0.6, s=10, label='Normal')
        axes[0,0].scatter(np.where(anomaly_mask)[0], df.loc[anomaly_mask, 'energy_consumption'],
                         c='red', s=30, label='Anomaly', marker='x')
        axes[0,0].set_title('(a) Isolation Forest Anomaly Detection', fontweight='bold')
        axes[0,0].set_xlabel('Time Index')
        axes[0,0].set_ylabel('Energy Consumption (kWh)')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)

        # 2. 统计异常检测
        axes[0,1].scatter(range(len(df)), df['energy_consumption'],
                         c='blue', alpha=0.6, s=10, label='Normal')
        axes[0,1].scatter(np.where(statistical_anomalies)[0],
                         df.loc[statistical_anomalies, 'energy_consumption'],
                         c='red', s=30, label='Statistical Anomaly', marker='x')
        axes[0,1].axhline(y=upper_bound, color='red', linestyle='--', alpha=0.7, label='Upper Bound')
        axes[0,1].axhline(y=lower_bound, color='red', linestyle='--', alpha=0.7, label='Lower Bound')
        axes[0,1].set_title('(b) Statistical Anomaly Detection (IQR)', fontweight='bold')
        axes[0,1].set_xlabel('Time Index')
        axes[0,1].set_ylabel('Energy Consumption (kWh)')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)

        # 3. 异常分数分布
        anomaly_scores = iso_forest.decision_function(X)
        axes[1,0].hist(anomaly_scores, bins=50, alpha=0.7, color=self.colors['primary'], edgecolor='black')
        axes[1,0].axvline(x=iso_forest.offset_, color='red', linestyle='--',
                         label=f'Threshold: {iso_forest.offset_:.3f}')
        axes[1,0].set_title('(c) Anomaly Score Distribution', fontweight='bold')
        axes[1,0].set_xlabel('Anomaly Score')
        axes[1,0].set_ylabel('Frequency')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)

        # 4. 特征空间中的异常点
        axes[1,1].scatter(df['temperature'], df['energy_consumption'],
                         c=anomalies, cmap='RdYlBu', alpha=0.6, s=30)
        axes[1,1].set_title('(d) Anomalies in Feature Space', fontweight='bold')
        axes[1,1].set_xlabel('Temperature (°C)')
        axes[1,1].set_ylabel('Energy Consumption (kWh)')
        cbar = plt.colorbar(axes[1,1].collections[0], ax=axes[1,1])
        cbar.set_label('Anomaly Score')
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{self.save_path}09_energy_anomaly_detection.png',
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()

    def plot_10_energy_forecasting_intervals(self):
        """图10: 能耗预测区间图"""
        df = self.data.copy()

        # 模拟预测数据（实际应用中这里会是模型预测结果）
        np.random.seed(42)
        n_forecast = 168  # 一周的小时数据

        # 生成模拟的预测数据
        actual = df['energy_consumption'].tail(n_forecast).values
        predicted = actual + np.random.normal(0, actual.std() * 0.1, n_forecast)

        # 计算预测区间
        residuals = actual - predicted
        std_residual = np.std(residuals)

        upper_95 = predicted + 1.96 * std_residual
        lower_95 = predicted - 1.96 * std_residual
        upper_80 = predicted + 1.28 * std_residual
        lower_80 = predicted - 1.28 * std_residual

        # 时间轴
        time_index = range(n_forecast)

        fig, axes = plt.subplots(2, 1, figsize=(16, 10))
        fig.suptitle('Energy Consumption Forecasting with Prediction Intervals',
                    fontsize=16, fontweight='bold')

        # 1. 预测区间图
        axes[0].fill_between(time_index, lower_95, upper_95, alpha=0.2,
                            color=self.colors['primary'], label='95% Confidence Interval')
        axes[0].fill_between(time_index, lower_80, upper_80, alpha=0.4,
                            color=self.colors['primary'], label='80% Confidence Interval')
        axes[0].plot(time_index, actual, 'o-', color='black', linewidth=2,
                    markersize=4, label='Actual', alpha=0.8)
        axes[0].plot(time_index, predicted, 's-', color=self.colors['secondary'],
                    linewidth=2, markersize=3, label='Predicted', alpha=0.8)

        axes[0].set_title('(a) Prediction Intervals and Actual vs Predicted Values', fontweight='bold')
        axes[0].set_xlabel('Time (Hours)')
        axes[0].set_ylabel('Energy Consumption (kWh)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # 2. 预测误差分析
        errors = actual - predicted

        # 误差时间序列
        axes[1].plot(time_index, errors, 'o-', color=self.colors['success'],
                    linewidth=1.5, markersize=3, alpha=0.7)
        axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[1].axhline(y=np.mean(errors), color='red', linestyle='--',
                       label=f'Mean Error: {np.mean(errors):.2f}')
        axes[1].axhline(y=np.mean(errors) + 2*np.std(errors), color='orange',
                       linestyle=':', label=f'+2σ: {np.mean(errors) + 2*np.std(errors):.2f}')
        axes[1].axhline(y=np.mean(errors) - 2*np.std(errors), color='orange',
                       linestyle=':', label=f'-2σ: {np.mean(errors) - 2*np.std(errors):.2f}')

        axes[1].set_title('(b) Prediction Error Analysis', fontweight='bold')
        axes[1].set_xlabel('Time (Hours)')
        axes[1].set_ylabel('Prediction Error (kWh)')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{self.save_path}10_energy_forecasting_intervals.png',
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()

    def generate_all_plots(self):
        """生成所有可视化图表"""
        print("🎨 开始生成高级可视化图表...")

        # 创建保存目录
        import os
        os.makedirs(self.save_path, exist_ok=True)

        try:
            print("📊 生成图表 1/10: 时间-能耗热力图...")
            self.plot_1_temporal_heatmap()

            print("📊 生成图表 2/10: 能耗流向桑基图...")
            self.plot_2_energy_flow_sankey()

            print("📊 生成图表 3/10: 3D能耗地形图...")
            self.plot_3_3d_energy_landscape()

            print("📊 生成图表 4/10: 统计分布分析...")
            self.plot_4_statistical_distribution_analysis()

            print("📊 生成图表 5/10: 相关性网络图...")
            self.plot_5_correlation_network()

            print("📊 生成图表 6/10: 能效雷达图...")
            self.plot_6_energy_efficiency_radar()

            print("📊 生成图表 7/10: 季节性分解...")
            self.plot_7_seasonal_decomposition()

            print("📊 生成图表 8/10: 聚类分析...")
            self.plot_8_energy_clustering_analysis()

            print("📊 生成图表 9/10: 异常检测...")
            self.plot_9_energy_anomaly_detection()

            print("📊 生成图表 10/10: 预测区间...")
            self.plot_10_energy_forecasting_intervals()

            print("✅ 所有10个高级图表生成完成！")

        except Exception as e:
            print(f"❌ 生成图表时出错: {e}")
            import traceback
            traceback.print_exc()


def create_advanced_visualizations(data, save_path="results/plots/"):
    """创建高级可视化图表的主函数"""
    visualizer = AdvancedBuildingEnergyVisualizer(data, save_path)
    visualizer.generate_all_plots()
    return visualizer
