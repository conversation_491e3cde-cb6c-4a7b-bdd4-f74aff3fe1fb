
# 🏢 建筑能耗预测项目摘要

## 📊 项目统计

- 📁 总目录数: 15
- 📄 总文件数: 20
- 🐍 代码文件数: 18

## 🎯 主要功能模块

### 1. 数据处理模块 (`src/data_processing/`)
- 数据预处理和清洗
- 特征工程
- 示例数据生成

### 2. 模型模块 (`src/models/`)
- CNN-LSTM混合模型
- 注意力机制
- 模型构建和编译

### 3. 训练模块 (`src/training/`)
- 模型训练流程
- 回调函数设置
- 训练历史记录

### 4. 评估模块 (`src/evaluation/`)
- 模型性能评估
- 预测结果分析
- 评估指标计算

### 5. 可视化模块 (`src/visualization/`)
- 10+种高级图表
- SCI级别分析图
- 交互式可视化

## 🚀 使用方式

### 零基础用户
```bash
python run_complete_project.py
```

### 交互式界面
```bash
python interactive_main.py
```

### 分步执行
```bash
# 1. 训练模型
python src/training/train_model.py

# 2. 评估模型  
python src/evaluation/evaluate_model.py
```

## 📈 输出结果

- `results/models/` - 训练好的模型
- `results/plots/` - 可视化图表
- `results/logs/` - 训练日志
- `results/` - 评估报告和预测结果
