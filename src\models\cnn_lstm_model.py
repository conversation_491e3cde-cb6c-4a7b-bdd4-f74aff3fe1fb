"""
CNN-LSTM混合模型用于建筑能耗预测
结合了CNN的局部特征提取能力和LSTM的时序建模能力
"""

import tensorflow as tf
from tensorflow.keras import layers, Model
from tensorflow.keras.layers import (
    Conv1D, MaxPooling1D, LSTM, Dense, Dropout, 
    BatchNormalization, Attention, Input, Concatenate
)
import numpy as np


class AttentionLayer(layers.Layer):
    """自定义注意力层"""
    
    def __init__(self, units, **kwargs):
        super(AttentionLayer, self).__init__(**kwargs)
        self.units = units
        self.W = Dense(units, activation='tanh')
        self.U = Dense(1, activation='softmax')
        
    def call(self, inputs):
        # inputs shape: (batch_size, time_steps, features)
        attention_weights = self.U(self.W(inputs))
        # attention_weights shape: (batch_size, time_steps, 1)
        
        # 应用注意力权重
        context_vector = tf.reduce_sum(attention_weights * inputs, axis=1)
        return context_vector, attention_weights
    
    def get_config(self):
        config = super().get_config()
        config.update({"units": self.units})
        return config


class CNNLSTMModel:
    """CNN-LSTM混合模型类"""
    
    def __init__(self, config):
        self.config = config
        self.model = None
        
    def build_model(self, input_shape):
        """构建CNN-LSTM混合模型"""
        
        # 输入层
        inputs = Input(shape=input_shape, name='input_layer')
        
        # CNN特征提取部分
        x = self._build_cnn_layers(inputs)
        
        # LSTM时序建模部分
        x = self._build_lstm_layers(x)
        
        # 注意力机制（可选）
        if self.config['model']['model_type'] == 'cnn_lstm_attention':
            x, attention_weights = self._build_attention_layer(x)
        
        # 全连接输出层
        outputs = self._build_dense_layers(x)
        
        # 创建模型
        self.model = Model(inputs=inputs, outputs=outputs, name='CNN_LSTM_Model')
        
        return self.model
    
    def _build_cnn_layers(self, inputs):
        """构建CNN层"""
        x = inputs
        
        cnn_config = self.config['model']['cnn']
        filters = cnn_config['filters']
        kernel_sizes = cnn_config['kernel_sizes']
        activation = cnn_config['activation']
        dropout = cnn_config['dropout']
        
        for i, (filter_num, kernel_size) in enumerate(zip(filters, kernel_sizes)):
            x = Conv1D(
                filters=filter_num,
                kernel_size=kernel_size,
                activation=activation,
                padding='same',
                name=f'conv1d_{i+1}'
            )(x)
            
            x = BatchNormalization(name=f'bn_conv_{i+1}')(x)
            x = Dropout(dropout, name=f'dropout_conv_{i+1}')(x)
            
            # 在前几层添加池化层
            if i < len(filters) - 1:
                x = MaxPooling1D(pool_size=2, name=f'maxpool_{i+1}')(x)
        
        return x
    
    def _build_lstm_layers(self, inputs):
        """构建LSTM层"""
        x = inputs
        
        lstm_config = self.config['model']['lstm']
        units = lstm_config['units']
        dropout = lstm_config['dropout']
        recurrent_dropout = lstm_config['recurrent_dropout']
        
        for i, unit_num in enumerate(units):
            return_sequences = (i < len(units) - 1) or \
                             (self.config['model']['model_type'] == 'cnn_lstm_attention')
            
            x = LSTM(
                units=unit_num,
                return_sequences=return_sequences,
                dropout=dropout,
                recurrent_dropout=recurrent_dropout,
                name=f'lstm_{i+1}'
            )(x)
            
            if return_sequences:
                x = BatchNormalization(name=f'bn_lstm_{i+1}')(x)
        
        return x
    
    def _build_attention_layer(self, inputs):
        """构建注意力层"""
        attention_config = self.config['model']['attention']
        units = attention_config['units']
        
        attention_layer = AttentionLayer(units, name='attention')
        context_vector, attention_weights = attention_layer(inputs)
        
        return context_vector, attention_weights
    
    def _build_dense_layers(self, inputs):
        """构建全连接输出层"""
        x = inputs
        
        dense_config = self.config['model']['dense']
        units = dense_config['units']
        activation = dense_config['activation']
        dropout = dense_config['dropout']
        
        for i, unit_num in enumerate(units[:-1]):
            x = Dense(
                units=unit_num,
                activation=activation,
                name=f'dense_{i+1}'
            )(x)
            x = Dropout(dropout, name=f'dropout_dense_{i+1}')(x)
        
        # 输出层
        outputs = Dense(
            units=units[-1],
            activation='linear',
            name='output_layer'
        )(x)
        
        return outputs
    
    def compile_model(self):
        """编译模型"""
        training_config = self.config['training']
        
        optimizer = self._get_optimizer(training_config)
        loss = training_config['loss']
        metrics = training_config['metrics']
        
        self.model.compile(
            optimizer=optimizer,
            loss=loss,
            metrics=metrics
        )
        
        return self.model
    
    def _get_optimizer(self, training_config):
        """获取优化器"""
        optimizer_name = training_config['optimizer']
        learning_rate = training_config['learning_rate']
        
        if optimizer_name == 'adam':
            return tf.keras.optimizers.Adam(learning_rate=learning_rate)
        elif optimizer_name == 'rmsprop':
            return tf.keras.optimizers.RMSprop(learning_rate=learning_rate)
        elif optimizer_name == 'sgd':
            return tf.keras.optimizers.SGD(learning_rate=learning_rate)
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_name}")
    
    def get_model_summary(self):
        """获取模型摘要"""
        if self.model is None:
            raise ValueError("Model not built yet. Call build_model() first.")
        
        return self.model.summary()


def create_model(config, input_shape):
    """创建并返回编译好的模型"""
    model_builder = CNNLSTMModel(config)
    model = model_builder.build_model(input_shape)
    model = model_builder.compile_model()
    
    return model, model_builder
