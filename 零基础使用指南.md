# 🏢 建筑能耗预测项目 - 零基础使用指南

## 📚 项目简介

这是一个使用深度学习技术预测建筑能耗的完整项目。即使你是编程零基础，也能通过这个指南成功运行项目！

### 🎯 项目能做什么？
- 预测建筑物未来的用电量
- 分析影响能耗的因素（温度、时间、季节等）
- 生成详细的预测报告和图表

### 🧠 使用的技术
- **CNN（卷积神经网络）**：提取数据中的模式
- **LSTM（长短期记忆网络）**：处理时间序列数据
- **注意力机制**：让模型关注重要信息

## 🚀 快速开始（5分钟上手）

### 第一步：环境准备

1. **确保你有Python**（建议3.8或更高版本）
   ```bash
   python --version
   ```

2. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```

### 第二步：一键运行

**选择以下任一方式：**

#### 方式1：完整流程（推荐新手）
```bash
python run_complete_project.py
```

#### 方式2：分步运行
```bash
# 1. 训练模型
python src/training/train_model.py

# 2. 评估模型
python src/evaluation/evaluate_model.py
```

### 第三步：查看结果

运行完成后，在 `results/` 文件夹中查看：
- 📊 预测结果图表
- 📈 模型性能指标
- 💾 训练好的模型文件

## 📁 项目结构说明

```
LSTM与CNN/
├── 📄 零基础使用指南.md        # 你正在看的文件
├── 📄 run_complete_project.py  # 一键运行脚本
├── 📄 config.yaml             # 配置文件
├── 📄 requirements.txt        # 依赖包列表
├── 📁 data/                   # 数据文件夹
│   ├── raw/                   # 原始数据
│   ├── processed/             # 处理后数据
│   └── sample/                # 示例数据
├── 📁 src/                    # 源代码
│   ├── models/                # 模型定义
│   ├── data_processing/       # 数据处理
│   ├── training/              # 训练脚本
│   └── evaluation/            # 评估脚本
├── 📁 notebooks/              # Jupyter教程
└── 📁 results/                # 结果输出
```

## 🔧 配置说明

主要配置在 `config.yaml` 文件中，新手可以直接使用默认设置：

### 重要参数说明：
- `sequence_length: 24` - 使用过去24小时的数据预测
- `epochs: 100` - 训练轮数（可以调小加快训练）
- `batch_size: 32` - 批次大小（内存不够可以调小）

### 如果想快速测试：
```yaml
training:
  epochs: 10        # 改为10轮快速测试
  batch_size: 16    # 减小批次大小
```

## 📊 结果解读

### 评估指标含义：
- **MAE（平均绝对误差）**：预测值与真实值的平均差距，越小越好
- **RMSE（均方根误差）**：预测误差的标准差，越小越好
- **MAPE（平均绝对百分比误差）**：预测误差的百分比，越小越好
- **R²（决定系数）**：模型解释数据的能力，越接近1越好

### 好的模型标准：
- MAPE < 10%：优秀
- MAPE < 20%：良好
- R² > 0.8：模型效果很好

## 🛠️ 常见问题解决

### Q1: 安装依赖时出错
```bash
# 尝试升级pip
python -m pip install --upgrade pip

# 或者使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### Q2: 内存不够
在 `config.yaml` 中调整：
```yaml
training:
  batch_size: 8     # 减小批次大小
  epochs: 20        # 减少训练轮数
```

### Q3: 训练太慢
```yaml
data:
  sequence_length: 12   # 减少序列长度
model:
  cnn:
    filters: [32, 64]   # 减少卷积核数量
  lstm:
    units: [64, 32]     # 减少LSTM单元数
```

### Q4: 想使用自己的数据
1. 将数据文件放在 `data/raw/` 文件夹
2. 确保数据格式包含以下列：
   - `timestamp`: 时间戳
   - `energy_consumption`: 能耗数据
   - `temperature`: 温度（可选）
   - `humidity`: 湿度（可选）

## 🎓 进阶学习

### 想深入了解？
1. 查看 `notebooks/` 文件夹中的教程
2. 阅读 `src/` 文件夹中的代码注释
3. 尝试修改 `config.yaml` 中的参数

### 想改进模型？
1. 添加更多特征（天气、节假日等）
2. 尝试不同的模型架构
3. 调整超参数

## 📞 获取帮助

如果遇到问题：
1. 检查错误信息
2. 查看 `results/training.log` 日志文件
3. 确认所有依赖包都已正确安装

## 🎉 成功标志

当你看到以下信息时，说明项目运行成功：
```
✅ 模型训练流程完成！
📁 结果保存在: results/
🎉 训练成功完成！
```

现在你就可以在 `results/` 文件夹中查看预测结果了！

---

**祝你使用愉快！如果这个项目对你有帮助，别忘了给个⭐！**
