"""
数据预处理模块
包含数据加载、清洗、特征工程、标准化等功能
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
import yaml
from typing import Tuple, Dict, List
import warnings
warnings.filterwarnings('ignore')


class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.scaler_X = None
        self.scaler_y = None
        
    def load_data(self, file_path: str) -> pd.DataFrame:
        """加载数据"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            else:
                raise ValueError("Unsupported file format. Use CSV or Excel.")
            
            print(f"数据加载成功，形状: {df.shape}")
            return df
        
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        print("开始数据清洗...")
        
        # 处理缺失值
        missing_before = df.isnull().sum().sum()
        
        # 对数值列使用前向填充和后向填充
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df[numeric_columns] = df[numeric_columns].fillna(method='ffill').fillna(method='bfill')
        
        # 删除仍有缺失值的行
        df = df.dropna()
        
        missing_after = df.isnull().sum().sum()
        print(f"缺失值处理: {missing_before} -> {missing_after}")
        
        # 处理异常值（使用IQR方法）
        for col in numeric_columns:
            if col != self.config['data']['target_column']:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers_before = len(df[(df[col] < lower_bound) | (df[col] > upper_bound)])
                df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]
                outliers_after = len(df[(df[col] < lower_bound) | (df[col] > upper_bound)])
                
                if outliers_before > 0:
                    print(f"{col}列异常值处理: {outliers_before} -> {outliers_after}")
        
        print(f"数据清洗完成，最终形状: {df.shape}")
        return df
    
    def feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征工程"""
        print("开始特征工程...")
        
        # 确保有时间戳列
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')
        elif df.index.dtype == 'object':
            df.index = pd.to_datetime(df.index)
        
        # 提取时间特征
        df['hour_of_day'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['month'] = df.index.month
        df['day_of_year'] = df.index.dayofyear
        
        # 创建周末标识
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        
        # 创建季节特征
        df['season'] = df['month'].apply(self._get_season)
        
        # 创建时间周期特征（正弦余弦编码）
        df['hour_sin'] = np.sin(2 * np.pi * df['hour_of_day'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour_of_day'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # 创建滞后特征
        target_col = self.config['data']['target_column']
        if target_col in df.columns:
            for lag in [1, 2, 3, 6, 12, 24]:
                df[f'{target_col}_lag_{lag}'] = df[target_col].shift(lag)
        
        # 创建滑动窗口统计特征
        for window in [3, 6, 12, 24]:
            df[f'{target_col}_rolling_mean_{window}'] = df[target_col].rolling(window=window).mean()
            df[f'{target_col}_rolling_std_{window}'] = df[target_col].rolling(window=window).std()
        
        # 删除因滞后和滑动窗口产生的缺失值
        df = df.dropna()
        
        print(f"特征工程完成，新形状: {df.shape}")
        print(f"新增特征: {list(df.columns)}")
        
        return df
    
    def _get_season(self, month: int) -> int:
        """根据月份获取季节"""
        if month in [12, 1, 2]:
            return 0  # 冬季
        elif month in [3, 4, 5]:
            return 1  # 春季
        elif month in [6, 7, 8]:
            return 2  # 夏季
        else:
            return 3  # 秋季
    
    def create_sequences(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """创建时间序列数据"""
        print("创建时间序列数据...")
        
        sequence_length = self.config['data']['sequence_length']
        target_column = self.config['data']['target_column']
        feature_columns = self.config['data']['feature_columns']
        
        # 选择特征列
        available_features = [col for col in feature_columns if col in df.columns]
        if len(available_features) != len(feature_columns):
            missing_features = set(feature_columns) - set(available_features)
            print(f"警告: 缺少特征列 {missing_features}")
        
        # 添加所有数值特征
        numeric_features = df.select_dtypes(include=[np.number]).columns.tolist()
        all_features = list(set(available_features + numeric_features))
        
        # 移除目标列
        if target_column in all_features:
            all_features.remove(target_column)
        
        X_data = df[all_features].values
        y_data = df[target_column].values
        
        X_sequences = []
        y_sequences = []
        
        for i in range(sequence_length, len(df)):
            X_sequences.append(X_data[i-sequence_length:i])
            y_sequences.append(y_data[i])
        
        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)
        
        print(f"序列数据创建完成:")
        print(f"X形状: {X_sequences.shape}")
        print(f"y形状: {y_sequences.shape}")
        
        return X_sequences, y_sequences
    
    def normalize_data(self, X_train: np.ndarray, X_val: np.ndarray, X_test: np.ndarray,
                      y_train: np.ndarray, y_val: np.ndarray, y_test: np.ndarray) -> Tuple:
        """数据标准化"""
        print("开始数据标准化...")
        
        normalization_method = self.config['data']['normalization']
        
        # 选择标准化方法
        if normalization_method == 'minmax':
            self.scaler_X = MinMaxScaler()
            self.scaler_y = MinMaxScaler()
        elif normalization_method == 'standard':
            self.scaler_X = StandardScaler()
            self.scaler_y = StandardScaler()
        elif normalization_method == 'robust':
            self.scaler_X = RobustScaler()
            self.scaler_y = RobustScaler()
        else:
            raise ValueError(f"Unsupported normalization method: {normalization_method}")
        
        # 重塑数据以适应scaler
        n_samples, n_timesteps, n_features = X_train.shape
        X_train_reshaped = X_train.reshape(-1, n_features)
        X_val_reshaped = X_val.reshape(-1, n_features)
        X_test_reshaped = X_test.reshape(-1, n_features)
        
        # 拟合并转换特征数据
        X_train_scaled = self.scaler_X.fit_transform(X_train_reshaped)
        X_val_scaled = self.scaler_X.transform(X_val_reshaped)
        X_test_scaled = self.scaler_X.transform(X_test_reshaped)
        
        # 重塑回原始形状
        X_train_scaled = X_train_scaled.reshape(n_samples, n_timesteps, n_features)
        X_val_scaled = X_val_scaled.reshape(X_val.shape[0], n_timesteps, n_features)
        X_test_scaled = X_test_scaled.reshape(X_test.shape[0], n_timesteps, n_features)
        
        # 标准化目标变量
        y_train_scaled = self.scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
        y_val_scaled = self.scaler_y.transform(y_val.reshape(-1, 1)).flatten()
        y_test_scaled = self.scaler_y.transform(y_test.reshape(-1, 1)).flatten()
        
        print("数据标准化完成")
        
        return (X_train_scaled, X_val_scaled, X_test_scaled,
                y_train_scaled, y_val_scaled, y_test_scaled)
    
    def split_data(self, X: np.ndarray, y: np.ndarray) -> Tuple:
        """分割数据集"""
        print("分割数据集...")
        
        train_ratio = self.config['data']['train_ratio']
        val_ratio = self.config['data']['val_ratio']
        test_ratio = self.config['data']['test_ratio']
        
        # 确保比例和为1
        total_ratio = train_ratio + val_ratio + test_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            print(f"警告: 数据分割比例和不为1 ({total_ratio})，将自动调整")
            train_ratio /= total_ratio
            val_ratio /= total_ratio
            test_ratio /= total_ratio
        
        # 第一次分割：分离训练集和临时集
        X_train, X_temp, y_train, y_temp = train_test_split(
            X, y, test_size=(val_ratio + test_ratio), 
            random_state=self.config['output']['random_seed'], shuffle=False
        )
        
        # 第二次分割：分离验证集和测试集
        val_size = val_ratio / (val_ratio + test_ratio)
        X_val, X_test, y_val, y_test = train_test_split(
            X_temp, y_temp, test_size=(1 - val_size),
            random_state=self.config['output']['random_seed'], shuffle=False
        )
        
        print(f"数据分割完成:")
        print(f"训练集: {X_train.shape[0]} 样本")
        print(f"验证集: {X_val.shape[0]} 样本")
        print(f"测试集: {X_test.shape[0]} 样本")
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def inverse_transform_predictions(self, predictions: np.ndarray) -> np.ndarray:
        """反标准化预测结果"""
        if self.scaler_y is None:
            raise ValueError("Scaler not fitted. Call normalize_data() first.")
        
        return self.scaler_y.inverse_transform(predictions.reshape(-1, 1)).flatten()
    
    def process_data(self, file_path: str) -> Dict:
        """完整的数据处理流程"""
        print("开始完整数据处理流程...")
        
        # 1. 加载数据
        df = self.load_data(file_path)
        if df is None:
            return None
        
        # 2. 数据清洗
        df = self.clean_data(df)
        
        # 3. 特征工程
        df = self.feature_engineering(df)
        
        # 4. 创建序列数据
        X, y = self.create_sequences(df)
        
        # 5. 分割数据
        X_train, X_val, X_test, y_train, y_val, y_test = self.split_data(X, y)
        
        # 6. 数据标准化
        X_train_scaled, X_val_scaled, X_test_scaled, y_train_scaled, y_val_scaled, y_test_scaled = \
            self.normalize_data(X_train, X_val, X_test, y_train, y_val, y_test)
        
        print("数据处理流程完成！")
        
        return {
            'X_train': X_train_scaled,
            'X_val': X_val_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train_scaled,
            'y_val': y_val_scaled,
            'y_test': y_test_scaled,
            'X_train_original': X_train,
            'X_val_original': X_val,
            'X_test_original': X_test,
            'y_train_original': y_train,
            'y_val_original': y_val,
            'y_test_original': y_test,
            'feature_names': df.select_dtypes(include=[np.number]).columns.tolist(),
            'input_shape': (X_train_scaled.shape[1], X_train_scaled.shape[2])
        }
