#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏢 建筑能耗预测项目 - 一键运行脚本

这个脚本会自动完成整个项目流程：
1. 检查环境和依赖
2. 生成示例数据
3. 训练模型
4. 评估模型性能
5. 生成预测报告

适合零基础用户一键运行！
"""

import os
import sys
import time
import subprocess
from datetime import datetime


def print_banner():
    """打印项目横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🏢 建筑能耗预测项目 - CNN+LSTM混合模型                ║
    ║                                                              ║
    ║        🎯 目标：预测建筑物未来的能耗                         ║
    ║        🧠 技术：深度学习 + 时间序列分析                      ║
    ║        👥 适用：零基础小白到专业人士                         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python环境...")
    
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，建议使用Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    required_packages = [
        'tensorflow',
        'numpy', 
        'pandas',
        'matplotlib',
        'seaborn',
        'scikit-learn',
        'pyyaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  发现缺失的包: {', '.join(missing_packages)}")
        print("正在尝试自动安装...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ])
            print("✅ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 自动安装失败，请手动运行: pip install -r requirements.txt")
            return False
    
    print("✅ 所有依赖包都已安装")
    return True


def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = [
        'data/raw',
        'data/processed', 
        'data/sample',
        'results/models',
        'results/plots',
        'results/logs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   📂 {directory}")
    
    print("✅ 目录创建完成")


def run_training():
    """运行模型训练"""
    print("\n🚀 开始模型训练...")
    print("   这可能需要几分钟时间，请耐心等待...")
    
    start_time = time.time()
    
    try:
        # 运行训练脚本
        result = subprocess.run([
            sys.executable, 'src/training/train_model.py'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            elapsed_time = time.time() - start_time
            print(f"✅ 模型训练完成！耗时: {elapsed_time:.1f}秒")
            return True
        else:
            print("❌ 训练过程中出现错误:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False


def run_evaluation():
    """运行模型评估"""
    print("\n📊 开始模型评估...")
    
    try:
        # 运行评估脚本
        result = subprocess.run([
            sys.executable, 'src/evaluation/evaluate_model.py'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 模型评估完成！")
            return True
        else:
            print("❌ 评估过程中出现错误:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        return False


def show_results():
    """显示结果摘要"""
    print("\n📈 项目运行结果:")
    print("=" * 60)
    
    # 检查结果文件
    results_dir = "results"
    
    if os.path.exists(results_dir):
        files = os.listdir(results_dir)
        
        print("📁 生成的文件:")
        for file in sorted(files):
            file_path = os.path.join(results_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"   📄 {file} ({size} bytes)")
        
        # 尝试读取评估指标
        metrics_file = os.path.join(results_dir, "evaluation_metrics.json")
        if os.path.exists(metrics_file):
            try:
                import json
                with open(metrics_file, 'r', encoding='utf-8') as f:
                    metrics = json.load(f)
                
                print("\n📊 模型性能指标:")
                for dataset, values in metrics.items():
                    print(f"\n   {dataset.upper()} 数据集:")
                    for metric, value in values.items():
                        if isinstance(value, float):
                            if metric == 'MAPE':
                                print(f"     {metric}: {value:.2f}%")
                            else:
                                print(f"     {metric}: {value:.4f}")
                
            except Exception as e:
                print(f"   ⚠️  无法读取评估指标: {e}")
    
    else:
        print("⚠️  未找到结果文件夹")


def print_next_steps():
    """打印后续步骤建议"""
    print("\n" + "=" * 60)
    print("🎉 项目运行完成！")
    print("\n📋 接下来你可以:")
    print("   1. 查看 results/ 文件夹中的详细结果")
    print("   2. 打开生成的图表文件查看可视化结果")
    print("   3. 阅读 零基础使用指南.md 了解更多")
    print("   4. 尝试修改 config.yaml 中的参数重新训练")
    print("   5. 使用自己的数据替换示例数据")
    
    print("\n💡 提示:")
    print("   - 如果想快速测试，可以在config.yaml中减少epochs数量")
    print("   - 如果内存不够，可以减小batch_size")
    print("   - 查看training.log了解训练详情")
    
    print("\n📚 学习资源:")
    print("   - notebooks/ 文件夹包含详细教程")
    print("   - src/ 文件夹包含完整源代码")
    print("   - README.md 包含项目详细说明")


def main():
    """主函数"""
    print_banner()
    
    # 记录开始时间
    start_time = datetime.now()
    print(f"🕐 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 步骤1: 检查环境
    if not check_python_version():
        return False
    
    if not check_dependencies():
        return False
    
    # 步骤2: 创建目录
    create_directories()
    
    # 步骤3: 训练模型
    if not run_training():
        print("\n❌ 项目运行失败：训练阶段出错")
        return False
    
    # 步骤4: 评估模型
    if not run_evaluation():
        print("\n⚠️  训练完成但评估失败，你仍然可以查看训练结果")
    
    # 步骤5: 显示结果
    show_results()
    
    # 计算总耗时
    end_time = datetime.now()
    total_time = end_time - start_time
    print(f"\n🕐 完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏱️  总耗时: {total_time}")
    
    # 打印后续步骤
    print_next_steps()
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 恭喜！项目运行成功！")
        else:
            print("\n😞 项目运行遇到问题，请查看错误信息")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断了程序运行")
        sys.exit(1)
    
    except Exception as e:
        print(f"\n💥 程序运行出现未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
