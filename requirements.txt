# 深度学习框架
tensorflow>=2.13.0
torch>=2.0.0
torchvision>=0.15.0

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# 可视化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# 配置管理
pyyaml>=6.0
configparser>=5.3.0

# 进度条和日志
tqdm>=4.65.0
loguru>=0.7.0

# Jupyter支持
jupyter>=1.0.0
ipykernel>=6.25.0

# 时间序列处理
statsmodels>=0.14.0

# 数据验证
pydantic>=2.0.0

# 模型保存和加载
joblib>=1.3.0
pickle5>=0.0.12

# 性能优化
numba>=0.57.0

# 交互式界面
tkinterdnd2>=0.3.0
pillow>=9.5.0

# 时间序列分析
statsmodels>=0.14.0

# 网络分析
networkx>=3.1

# 异常检测
scikit-learn>=1.3.0

# 可选：GPU加速（如果有NVIDIA GPU）
# tensorflow-gpu>=2.13.0
# torch-audio>=2.0.0
