#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏢 建筑能耗预测项目 - 交互式主界面
支持拖拽文件输入的完整功能界面

功能包括：
1. 拖拽文件输入
2. 数据预处理和分析
3. 模型训练
4. 高级可视化
5. 预测评估
6. 结果导出
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from tkinterdnd2 import DND_FILES, TkinterDnD
import os
import sys
import threading
import queue
import pandas as pd
import numpy as np
from datetime import datetime
import json

# 添加项目路径
sys.path.append('.')
sys.path.append('src')

from src.data_processing.data_preprocessor import DataPreprocessor
from src.data_processing.generate_sample_data import generate_sample_building_energy_data
from src.training.train_model import ModelTrainer
from src.evaluation.evaluate_model import ModelEvaluator
from src.visualization.advanced_plots import create_advanced_visualizations


class BuildingEnergyPredictionGUI:
    """建筑能耗预测交互式界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🏢 建筑能耗预测系统 - 交互式界面")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 数据存储
        self.data_file = None
        self.processed_data = None
        self.model_trainer = None
        self.model_evaluator = None
        
        # 消息队列用于线程通信
        self.message_queue = queue.Queue()
        
        # 创建界面
        self.create_widgets()
        self.setup_drag_drop()
        
        # 启动消息处理
        self.process_queue()
        
    def create_widgets(self):
        """创建界面组件"""
        
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2E86AB', height=80)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="🏢 建筑能耗预测系统 - CNN+LSTM混合模型",
                              font=('Arial', 18, 'bold'),
                              fg='white', bg='#2E86AB')
        title_label.pack(expand=True)
        
        # 创建主要区域
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 左侧控制面板
        self.create_control_panel(main_frame)
        
        # 右侧日志和结果面板
        self.create_log_panel(main_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = tk.LabelFrame(parent, text="📋 控制面板", 
                                     font=('Arial', 12, 'bold'),
                                     bg='#f0f0f0', fg='#2E86AB')
        control_frame.pack(side='left', fill='y', padx=(0, 5))
        
        # 文件输入区域
        file_frame = tk.LabelFrame(control_frame, text="📁 数据输入", 
                                  font=('Arial', 10, 'bold'))
        file_frame.pack(fill='x', padx=5, pady=5)
        
        # 拖拽区域
        self.drop_area = tk.Label(file_frame, 
                                 text="🎯 拖拽CSV文件到这里\n或点击选择文件\n\n支持格式：CSV, Excel",
                                 width=30, height=6,
                                 bg='#E8F4FD', fg='#2E86AB',
                                 font=('Arial', 10),
                                 relief='dashed', bd=2)
        self.drop_area.pack(padx=10, pady=10)
        
        # 文件选择按钮
        tk.Button(file_frame, text="📂 选择文件", 
                 command=self.select_file,
                 bg='#2E86AB', fg='white',
                 font=('Arial', 10, 'bold')).pack(pady=5)
        
        # 使用示例数据按钮
        tk.Button(file_frame, text="🎲 使用示例数据", 
                 command=self.use_sample_data,
                 bg='#F18F01', fg='white',
                 font=('Arial', 10, 'bold')).pack(pady=5)
        
        # 当前文件显示
        self.file_label = tk.Label(file_frame, text="未选择文件", 
                                  fg='gray', font=('Arial', 9))
        self.file_label.pack(pady=5)
        
        # 功能按钮区域
        function_frame = tk.LabelFrame(control_frame, text="🚀 功能操作", 
                                      font=('Arial', 10, 'bold'))
        function_frame.pack(fill='x', padx=5, pady=5)
        
        # 按钮列表
        buttons = [
            ("📊 数据分析", self.analyze_data, '#A23B72'),
            ("🎨 生成可视化", self.create_visualizations, '#7209B7'),
            ("🧠 训练模型", self.train_model, '#C73E1D'),
            ("📈 评估模型", self.evaluate_model, '#F4A261'),
            ("🔄 完整流程", self.run_complete_pipeline, '#264653'),
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(function_frame, text=text, command=command,
                           bg=color, fg='white', font=('Arial', 10, 'bold'),
                           width=20, height=2)
            btn.pack(pady=3, padx=5)
        
        # 进度条
        self.progress_frame = tk.LabelFrame(control_frame, text="📊 进度", 
                                           font=('Arial', 10, 'bold'))
        self.progress_frame.pack(fill='x', padx=5, pady=5)
        
        self.progress_var = tk.StringVar(value="等待开始...")
        self.progress_label = tk.Label(self.progress_frame, 
                                      textvariable=self.progress_var,
                                      font=('Arial', 9))
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(self.progress_frame, 
                                           mode='indeterminate')
        self.progress_bar.pack(fill='x', padx=10, pady=5)
        
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = tk.LabelFrame(parent, text="📝 运行日志", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#f0f0f0', fg='#2E86AB')
        log_frame.pack(side='right', fill='both', expand=True)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, 
                                                 width=60, height=25,
                                                 font=('Consolas', 10),
                                                 bg='#1e1e1e', fg='#ffffff')
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 底部按钮
        button_frame = tk.Frame(log_frame, bg='#f0f0f0')
        button_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Button(button_frame, text="🗑️ 清空日志", 
                 command=self.clear_log,
                 bg='#C73E1D', fg='white').pack(side='left')
        
        tk.Button(button_frame, text="💾 保存日志", 
                 command=self.save_log,
                 bg='#2E86AB', fg='white').pack(side='left', padx=5)
        
        tk.Button(button_frame, text="📁 打开结果文件夹", 
                 command=self.open_results_folder,
                 bg='#F18F01', fg='white').pack(side='right')
        
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.drop_area.drop_target_register(DND_FILES)
        self.drop_area.dnd_bind('<<Drop>>', self.on_file_drop)
        
    def on_file_drop(self, event):
        """处理文件拖拽"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if file_path.lower().endswith(('.csv', '.xlsx', '.xls')):
                self.data_file = file_path
                self.file_label.config(text=f"已选择: {os.path.basename(file_path)}")
                self.log(f"✅ 文件已加载: {file_path}")
            else:
                messagebox.showerror("错误", "请选择CSV或Excel文件！")
                
    def select_file(self):
        """选择文件对话框"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV files", "*.csv"), 
                      ("Excel files", "*.xlsx"), 
                      ("All files", "*.*")]
        )
        if file_path:
            self.data_file = file_path
            self.file_label.config(text=f"已选择: {os.path.basename(file_path)}")
            self.log(f"✅ 文件已选择: {file_path}")
            
    def use_sample_data(self):
        """使用示例数据"""
        try:
            self.log("🎲 正在生成示例数据...")
            sample_path = "data/sample/building_energy_sample.csv"
            os.makedirs(os.path.dirname(sample_path), exist_ok=True)
            
            generate_sample_building_energy_data(save_path=sample_path)
            self.data_file = sample_path
            self.file_label.config(text="已选择: 示例数据")
            self.log("✅ 示例数据生成完成！")
            
        except Exception as e:
            self.log(f"❌ 生成示例数据失败: {e}")
            messagebox.showerror("错误", f"生成示例数据失败: {e}")
            
    def analyze_data(self):
        """分析数据"""
        if not self.data_file:
            messagebox.showwarning("警告", "请先选择数据文件！")
            return
            
        def analyze():
            try:
                self.update_progress("正在分析数据...")
                self.log("📊 开始数据分析...")
                
                # 加载数据
                if self.data_file.endswith('.csv'):
                    df = pd.read_csv(self.data_file)
                else:
                    df = pd.read_excel(self.data_file)
                
                # 基本信息
                self.log(f"📋 数据形状: {df.shape}")
                self.log(f"📅 列名: {list(df.columns)}")
                
                # 统计信息
                if 'energy_consumption' in df.columns:
                    energy_stats = df['energy_consumption'].describe()
                    self.log("📈 能耗统计信息:")
                    for stat, value in energy_stats.items():
                        self.log(f"   {stat}: {value:.2f}")
                
                # 缺失值检查
                missing = df.isnull().sum()
                if missing.sum() > 0:
                    self.log("⚠️ 发现缺失值:")
                    for col, count in missing[missing > 0].items():
                        self.log(f"   {col}: {count} 个缺失值")
                else:
                    self.log("✅ 无缺失值")
                
                self.update_progress("数据分析完成")
                self.log("✅ 数据分析完成！")
                
            except Exception as e:
                self.log(f"❌ 数据分析失败: {e}")
                messagebox.showerror("错误", f"数据分析失败: {e}")
            finally:
                self.stop_progress()
                
        self.start_progress()
        threading.Thread(target=analyze, daemon=True).start()
        
    def create_visualizations(self):
        """创建可视化"""
        if not self.data_file:
            messagebox.showwarning("警告", "请先选择数据文件！")
            return
            
        def visualize():
            try:
                self.update_progress("正在生成可视化图表...")
                self.log("🎨 开始生成高级可视化图表...")
                
                # 加载数据
                if self.data_file.endswith('.csv'):
                    df = pd.read_csv(self.data_file)
                else:
                    df = pd.read_excel(self.data_file)
                
                # 创建可视化
                create_advanced_visualizations(df, "results/plots/")
                
                self.update_progress("可视化生成完成")
                self.log("✅ 高级可视化图表生成完成！")
                self.log("📁 图表保存在 results/plots/ 文件夹中")
                
            except Exception as e:
                self.log(f"❌ 可视化生成失败: {e}")
                messagebox.showerror("错误", f"可视化生成失败: {e}")
            finally:
                self.stop_progress()
                
        self.start_progress()
        threading.Thread(target=visualize, daemon=True).start()
        
    def train_model(self):
        """训练模型"""
        if not self.data_file:
            messagebox.showwarning("警告", "请先选择数据文件！")
            return
            
        def train():
            try:
                self.update_progress("正在训练模型...")
                self.log("🧠 开始模型训练...")
                
                self.model_trainer = ModelTrainer()
                success = self.model_trainer.run_complete_training(self.data_file)
                
                if success:
                    self.update_progress("模型训练完成")
                    self.log("✅ 模型训练完成！")
                else:
                    self.log("❌ 模型训练失败")
                    
            except Exception as e:
                self.log(f"❌ 模型训练失败: {e}")
                messagebox.showerror("错误", f"模型训练失败: {e}")
            finally:
                self.stop_progress()
                
        self.start_progress()
        threading.Thread(target=train, daemon=True).start()
        
    def evaluate_model(self):
        """评估模型"""
        def evaluate():
            try:
                self.update_progress("正在评估模型...")
                self.log("📈 开始模型评估...")
                
                self.model_evaluator = ModelEvaluator()
                success = self.model_evaluator.run_complete_evaluation(data_file=self.data_file)
                
                if success:
                    self.update_progress("模型评估完成")
                    self.log("✅ 模型评估完成！")
                else:
                    self.log("❌ 模型评估失败")
                    
            except Exception as e:
                self.log(f"❌ 模型评估失败: {e}")
                messagebox.showerror("错误", f"模型评估失败: {e}")
            finally:
                self.stop_progress()
                
        self.start_progress()
        threading.Thread(target=evaluate, daemon=True).start()
        
    def run_complete_pipeline(self):
        """运行完整流程"""
        if not self.data_file:
            messagebox.showwarning("警告", "请先选择数据文件！")
            return
            
        def complete_pipeline():
            try:
                self.log("🔄 开始完整流程...")
                
                # 1. 数据分析
                self.update_progress("步骤 1/4: 数据分析...")
                self.message_queue.put(("analyze", None))
                
                # 2. 可视化
                self.update_progress("步骤 2/4: 生成可视化...")
                self.message_queue.put(("visualize", None))
                
                # 3. 训练模型
                self.update_progress("步骤 3/4: 训练模型...")
                self.message_queue.put(("train", None))
                
                # 4. 评估模型
                self.update_progress("步骤 4/4: 评估模型...")
                self.message_queue.put(("evaluate", None))
                
                self.update_progress("完整流程完成")
                self.log("🎉 完整流程执行完成！")
                
            except Exception as e:
                self.log(f"❌ 完整流程执行失败: {e}")
            finally:
                self.stop_progress()
                
        self.start_progress()
        threading.Thread(target=complete_pipeline, daemon=True).start()
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def save_log(self):
        """保存日志"""
        log_content = self.log_text.get(1.0, tk.END)
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(log_content)
            self.log(f"💾 日志已保存到: {file_path}")
            
    def open_results_folder(self):
        """打开结果文件夹"""
        results_path = "results"
        if os.path.exists(results_path):
            os.startfile(results_path)
        else:
            messagebox.showinfo("提示", "结果文件夹不存在，请先运行分析！")
            
    def start_progress(self):
        """开始进度条"""
        self.progress_bar.start(10)
        
    def stop_progress(self):
        """停止进度条"""
        self.progress_bar.stop()
        
    def update_progress(self, message):
        """更新进度信息"""
        self.progress_var.set(message)
        
    def process_queue(self):
        """处理消息队列"""
        try:
            while True:
                action, data = self.message_queue.get_nowait()
                # 处理队列中的消息
                if action == "analyze":
                    self.analyze_data()
                elif action == "visualize":
                    self.create_visualizations()
                elif action == "train":
                    self.train_model()
                elif action == "evaluate":
                    self.evaluate_model()
        except queue.Empty:
            pass
        
        # 继续处理队列
        self.root.after(100, self.process_queue)


def main():
    """主函数"""
    # 创建主窗口
    root = TkinterDnD.Tk()
    
    # 创建应用
    app = BuildingEnergyPredictionGUI(root)
    
    # 启动界面
    root.mainloop()


if __name__ == "__main__":
    main()
