# LSTM与CNN结合的建筑能耗预测项目

## 项目概述

本项目实现了一个基于深度学习的建筑能耗预测系统，结合了CNN和LSTM的优势：
- **CNN层**：提取时间序列的局部特征和模式
- **LSTM层**：捕获长期时间依赖关系
- **注意力机制**：增强模型对关键特征的关注能力

## 技术特点

- 🔥 **混合架构**：CNN-LSTM-Attention组合模型
- 📊 **多变量预测**：支持温度、湿度、时间等多个特征
- 🎯 **高精度预测**：基于最新研究的优化架构
- 📈 **完整评估**：MAE、RMSE、MAPE等多种评估指标
- 🎨 **可视化分析**：预测结果和模型性能可视化

## 项目结构

```
├── data/                   # 数据目录
│   ├── raw/               # 原始数据
│   ├── processed/         # 处理后的数据
│   └── sample/            # 示例数据
├── src/                   # 源代码
│   ├── models/            # 模型定义
│   ├── data_processing/   # 数据处理
│   ├── training/          # 训练脚本
│   └── evaluation/        # 评估脚本
├── notebooks/             # Jupyter笔记本
├── results/               # 结果输出
├── requirements.txt       # 依赖包
└── config.yaml           # 配置文件
```

## 快速开始

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **准备数据**
```bash
python src/data_processing/prepare_data.py
```

3. **训练模型**
```bash
python src/training/train_model.py
```

4. **评估预测**
```bash
python src/evaluation/evaluate_model.py
```

## 模型架构

### CNN-LSTM-Attention混合模型
1. **输入层**：多变量时间序列数据
2. **CNN层**：1D卷积提取局部特征
3. **LSTM层**：捕获时间序列依赖关系
4. **注意力层**：增强重要特征权重
5. **全连接层**：输出预测结果

### 关键创新点
- 自适应特征提取
- 多尺度时间建模
- 注意力机制优化
- 残差连接增强

## 数据格式

支持的输入数据格式：
- CSV文件，包含时间戳和多个特征列
- 必需列：timestamp, energy_consumption
- 可选列：temperature, humidity, day_of_week, hour_of_day等

## 性能指标

- **MAE (Mean Absolute Error)**：平均绝对误差
- **RMSE (Root Mean Square Error)**：均方根误差
- **MAPE (Mean Absolute Percentage Error)**：平均绝对百分比误差
- **R² Score**：决定系数

## 参考文献

基于以下最新研究成果：
1. "A Hybrid CNN-LSTM Model for Predicting Energy Consumption" (2024)
2. "Energy consumption prediction using modified deep CNN-Bi LSTM" (2025)
3. "Comparative analysis of deep neural network architectures for renewable energy forecasting" (2024)

## 作者

建筑能耗预测项目团队

## 许可证

MIT License
